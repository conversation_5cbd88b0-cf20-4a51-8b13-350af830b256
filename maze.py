import pygame

class Maze:
    def __init__(self, width, height, grid_size):
        self.width = width
        self.height = height
        self.grid_size = grid_size
        self.grid_width = width // grid_size
        self.grid_height = height // grid_size
        
        # 地图元素定义
        # 0 = 空地, 1 = 墙壁, 2 = 豆子, 3 = 大豆子
        self.maze_layout = [
            [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
            [1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,1,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,1],
            [1,3,1,1,1,1,2,1,1,1,1,1,2,1,1,1,1,1,2,1,1,2,1,1,1,1,1,2,1,1,1,1,1,2,1,1,1,1,3,1],
            [1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,1],
            [1,2,1,1,1,1,2,1,1,2,1,1,1,1,1,1,2,1,1,1,1,1,1,2,1,1,1,1,1,1,2,1,1,2,1,1,1,1,2,1],
            [1,2,2,2,2,2,2,1,1,2,2,2,2,1,1,2,2,2,2,1,1,2,2,2,2,1,1,2,2,2,2,1,1,2,2,2,2,2,2,1],
            [1,1,1,1,1,1,2,1,1,1,1,1,0,1,1,2,1,1,2,1,1,2,1,1,2,1,1,0,1,1,1,1,1,2,1,1,1,1,1,1],
            [0,0,0,0,0,1,2,1,1,2,2,2,2,2,2,2,1,1,2,1,1,2,1,1,2,2,2,2,2,2,2,1,1,2,1,0,0,0,0,0],
            [1,1,1,1,1,1,2,1,1,2,1,1,0,0,0,0,1,1,2,1,1,2,1,1,0,0,0,0,1,1,2,1,1,2,1,1,1,1,1,1],
            [2,2,2,2,2,2,2,2,2,2,1,0,0,0,0,0,0,2,2,0,0,2,2,0,0,0,0,0,0,1,2,2,2,2,2,2,2,2,2,2],
            [1,1,1,1,1,1,2,1,1,2,1,0,0,0,0,0,1,1,1,0,0,1,1,1,0,0,0,0,0,1,2,1,1,2,1,1,1,1,1,1],
            [0,0,0,0,0,1,2,1,1,2,1,1,1,1,1,1,1,0,0,0,0,0,0,1,1,1,1,1,1,1,2,1,1,2,1,0,0,0,0,0],
            [1,1,1,1,1,1,2,1,1,2,2,2,2,2,2,2,1,0,0,0,0,0,0,1,2,2,2,2,2,2,2,1,1,2,1,1,1,1,1,1],
            [1,2,2,2,2,2,2,1,1,1,1,1,0,1,1,2,1,1,1,1,1,1,1,1,2,1,1,0,1,1,1,1,1,2,2,2,2,2,2,1],
            [1,2,1,1,1,1,2,2,2,2,2,2,2,1,1,2,2,2,2,1,1,2,2,2,2,1,1,2,2,2,2,2,2,2,1,1,1,1,2,1],
            [1,2,2,2,2,2,2,1,1,2,1,1,1,1,1,1,2,1,1,1,1,1,1,2,1,1,1,1,1,1,2,1,1,2,2,2,2,2,2,1],
            [1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,1],
            [1,3,1,1,1,1,2,1,1,1,1,1,2,1,1,1,1,1,2,1,1,2,1,1,1,1,1,2,1,1,1,1,1,2,1,1,1,1,3,1],
            [1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,1,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,1],
            [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]
        ]
        
        # 颜色定义
        self.WALL_COLOR = (0, 0, 255)
        self.DOT_COLOR = (255, 255, 0)
        self.BIG_DOT_COLOR = (255, 255, 255)
        self.PATH_COLOR = (0, 0, 0)
        
    def get_cell(self, x, y):
        """获取指定位置的单元格类型"""
        if 0 <= y < len(self.maze_layout) and 0 <= x < len(self.maze_layout[0]):
            return self.maze_layout[y][x]
        return 1  # 边界外视为墙壁
    
    def set_cell(self, x, y, value):
        """设置指定位置的单元格类型"""
        if 0 <= y < len(self.maze_layout) and 0 <= x < len(self.maze_layout[0]):
            self.maze_layout[y][x] = value
    
    def is_wall(self, x, y):
        """检查指定位置是否为墙壁"""
        return self.get_cell(x, y) == 1
    
    def is_dot(self, x, y):
        """检查指定位置是否为豆子"""
        return self.get_cell(x, y) == 2
    
    def is_big_dot(self, x, y):
        """检查指定位置是否为大豆子"""
        return self.get_cell(x, y) == 3
    
    def eat_dot(self, x, y):
        """吃掉指定位置的豆子"""
        if self.is_dot(x, y):
            self.set_cell(x, y, 0)
            return 10  # 普通豆子得分
        elif self.is_big_dot(x, y):
            self.set_cell(x, y, 0)
            return 50  # 大豆子得分
        return 0
    
    def count_dots(self):
        """计算剩余豆子数量"""
        count = 0
        for row in self.maze_layout:
            for cell in row:
                if cell == 2 or cell == 3:
                    count += 1
        return count
    
    def draw(self, screen, offset_y=0):
        """绘制迷宫"""
        for y, row in enumerate(self.maze_layout):
            for x, cell in enumerate(row):
                rect = pygame.Rect(x * self.grid_size, y * self.grid_size + offset_y,
                                 self.grid_size, self.grid_size)
                
                if cell == 1:  # 墙壁
                    pygame.draw.rect(screen, self.WALL_COLOR, rect)
                elif cell == 2:  # 普通豆子
                    center = (x * self.grid_size + self.grid_size // 2,
                             y * self.grid_size + self.grid_size // 2 + offset_y)
                    pygame.draw.circle(screen, self.DOT_COLOR, center, 3)
                elif cell == 3:  # 大豆子
                    center = (x * self.grid_size + self.grid_size // 2,
                             y * self.grid_size + self.grid_size // 2 + offset_y)
                    pygame.draw.circle(screen, self.BIG_DOT_COLOR, center, 8)
