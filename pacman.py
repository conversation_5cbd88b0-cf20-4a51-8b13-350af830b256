import pygame
import math

class Pacman:
    def __init__(self, x, y, grid_size):
        self.grid_x = x
        self.grid_y = y
        self.grid_size = grid_size
        self.pixel_x = x * grid_size + grid_size // 2
        self.pixel_y = y * grid_size + grid_size // 2
        
        # 移动相关
        self.direction = 0  # 0=右, 1=下, 2=左, 3=上
        self.next_direction = 0
        self.speed = 2
        self.moving = False
        
        # 动画相关
        self.mouth_angle = 0
        self.mouth_opening = True
        self.animation_speed = 0.3
        
        # 颜色
        self.color = (255, 255, 0)  # 黄色
        
        # 方向向量
        self.directions = [
            (1, 0),   # 右
            (0, 1),   # 下
            (-1, 0),  # 左
            (0, -1)   # 上
        ]
    
    def set_direction(self, direction):
        """设置下一个移动方向"""
        self.next_direction = direction
    
    def can_move(self, maze, dx, dy):
        """检查是否可以移动到指定位置"""
        new_x = self.grid_x + dx
        new_y = self.grid_y + dy
        return not maze.is_wall(new_x, new_y)
    
    def update(self, maze):
        """更新吃豆人状态"""
        # 更新嘴巴动画
        if self.mouth_opening:
            self.mouth_angle += self.animation_speed
            if self.mouth_angle >= 1.0:
                self.mouth_opening = False
        else:
            self.mouth_angle -= self.animation_speed
            if self.mouth_angle <= 0:
                self.mouth_opening = True
        
        # 检查是否可以改变方向
        dx, dy = self.directions[self.next_direction]
        if self.can_move(maze, dx, dy):
            self.direction = self.next_direction
        
        # 移动
        dx, dy = self.directions[self.direction]
        if self.can_move(maze, dx, dy):
            self.pixel_x += dx * self.speed
            self.pixel_y += dy * self.speed
            
            # 检查是否到达网格中心
            target_x = self.grid_x * self.grid_size + self.grid_size // 2
            target_y = self.grid_y * self.grid_size + self.grid_size // 2
            
            if dx > 0 and self.pixel_x >= target_x + self.grid_size:
                self.grid_x += 1
                self.pixel_x = self.grid_x * self.grid_size + self.grid_size // 2
            elif dx < 0 and self.pixel_x <= target_x - self.grid_size:
                self.grid_x -= 1
                self.pixel_x = self.grid_x * self.grid_size + self.grid_size // 2
            elif dy > 0 and self.pixel_y >= target_y + self.grid_size:
                self.grid_y += 1
                self.pixel_y = self.grid_y * self.grid_size + self.grid_size // 2
            elif dy < 0 and self.pixel_y <= target_y - self.grid_size:
                self.grid_y -= 1
                self.pixel_y = self.grid_y * self.grid_size + self.grid_size // 2
    
    def eat_dot(self, maze):
        """吃豆子"""
        return maze.eat_dot(self.grid_x, self.grid_y)
    
    def draw(self, screen):
        """绘制吃豆人"""
        radius = self.grid_size // 2 - 2
        
        # 计算嘴巴角度
        mouth_size = int(self.mouth_angle * 60)  # 最大60度
        
        # 根据方向调整起始角度
        start_angle = 0
        if self.direction == 0:    # 右
            start_angle = mouth_size
        elif self.direction == 1:  # 下
            start_angle = 90 + mouth_size
        elif self.direction == 2:  # 左
            start_angle = 180 + mouth_size
        elif self.direction == 3:  # 上
            start_angle = 270 + mouth_size
        
        end_angle = start_angle + (360 - 2 * mouth_size)
        
        # 绘制吃豆人身体
        if mouth_size > 5:  # 只有当嘴巴足够大时才绘制扇形
            # 创建点列表来绘制扇形
            points = [(self.pixel_x, self.pixel_y)]
            
            # 添加圆弧上的点
            for angle in range(int(start_angle), int(end_angle) + 1, 5):
                x = self.pixel_x + radius * math.cos(math.radians(angle))
                y = self.pixel_y + radius * math.sin(math.radians(angle))
                points.append((x, y))
            
            if len(points) > 2:
                pygame.draw.polygon(screen, self.color, points)
        else:
            # 嘴巴闭合时绘制完整圆形
            pygame.draw.circle(screen, self.color, (int(self.pixel_x), int(self.pixel_y)), radius)
    
    def get_position(self):
        """获取当前网格位置"""
        return self.grid_x, self.grid_y
