#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
吃豆子游戏新功能测试脚本
测试动态分数显示、返回首页功能和保存进度功能
"""

import pygame
import sys
import json
import os
from game import PacmanGame

def test_score_display():
    """测试分数显示功能"""
    print("🧪 测试分数显示功能...")
    
    # 创建游戏实例
    game = PacmanGame()
    
    # 检查分数初始化
    assert game.score == 0, "初始分数应为0"
    assert game.lives == 3, "初始生命数应为3"
    assert game.level == 1, "初始关卡应为1"
    
    # 模拟得分
    game.score = 100
    game.lives = 2
    game.level = 2
    
    print("✅ 分数显示功能测试通过")

def test_save_load_progress():
    """测试保存和加载进度功能"""
    print("🧪 测试保存和加载进度功能...")
    
    # 创建游戏实例
    game = PacmanGame()
    
    # 设置测试数据
    game.score = 1500
    game.lives = 2
    game.level = 3
    game.game_time = 3600
    game.pacman.grid_x = 5
    game.pacman.grid_y = 10
    
    # 测试保存功能
    game.save_game_progress()
    
    # 检查保存文件是否存在
    assert os.path.exists("game_progress.json"), "保存文件应该存在"
    
    # 测试has_saved_game功能
    assert game.has_saved_game(), "应该检测到保存的游戏"
    
    # 创建新游戏实例测试加载
    new_game = PacmanGame()
    success = new_game.load_game_progress()
    
    assert success, "应该成功加载游戏进度"
    assert new_game.score == 1500, f"分数应为1500，实际为{new_game.score}"
    assert new_game.lives == 2, f"生命数应为2，实际为{new_game.lives}"
    assert new_game.level == 3, f"关卡应为3，实际为{new_game.level}"
    assert new_game.game_time == 3600, f"游戏时间应为3600，实际为{new_game.game_time}"
    
    # 测试删除保存功能
    new_game.delete_saved_game()
    assert not os.path.exists("game_progress.json"), "保存文件应该被删除"
    
    print("✅ 保存和加载进度功能测试通过")

def test_game_states():
    """测试游戏状态管理"""
    print("🧪 测试游戏状态管理...")
    
    game = PacmanGame()
    
    # 测试初始状态
    assert game.game_state == "START", f"初始状态应为START，实际为{game.game_state}"
    
    # 测试开始游戏
    game.start_game()
    assert game.game_state == "PLAYING", f"开始游戏后状态应为PLAYING，实际为{game.game_state}"
    
    # 测试返回首页
    game.return_to_home()
    assert game.game_state == "START", f"返回首页后状态应为START，实际为{game.game_state}"
    
    print("✅ 游戏状态管理测试通过")

def test_high_score():
    """测试最高分功能"""
    print("🧪 测试最高分功能...")
    
    game = PacmanGame()
    
    # 测试初始最高分
    initial_high_score = game.high_score
    
    # 设置新的高分
    game.score = initial_high_score + 1000
    game.high_score = game.score
    game.save_high_score()
    
    # 创建新游戏实例测试加载
    new_game = PacmanGame()
    assert new_game.high_score >= initial_high_score + 1000, "最高分应该被正确保存和加载"
    
    print("✅ 最高分功能测试通过")

def test_font_loading():
    """测试字体加载功能"""
    print("🧪 测试字体加载功能...")
    
    game = PacmanGame()
    
    # 检查字体是否成功加载
    assert game.font is not None, "普通字体应该成功加载"
    assert game.big_font is not None, "大字体应该成功加载"
    assert game.small_font is not None, "小字体应该成功加载"
    
    # 测试中文文本渲染
    try:
        test_text = game.font.render("测试中文", True, (255, 255, 255))
        assert test_text is not None, "应该能够渲染中文文本"
    except Exception as e:
        print(f"⚠️  中文渲染警告: {e}")
    
    print("✅ 字体加载功能测试通过")

def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("🧪 吃豆子游戏新功能测试")
    print("=" * 60)
    
    try:
        # 初始化pygame（测试需要）
        pygame.init()
        
        # 运行各项测试
        test_score_display()
        test_save_load_progress()
        test_game_states()
        test_high_score()
        test_font_loading()
        
        print("=" * 60)
        print("🎉 所有测试通过！")
        print("✅ 动态分数显示功能正常")
        print("✅ 返回首页功能正常")
        print("✅ 保存进度功能正常")
        print("✅ 游戏状态管理正常")
        print("✅ 最高分功能正常")
        print("✅ 字体加载功能正常")
        print("=" * 60)
        
        return True
        
    except AssertionError as e:
        print(f"❌ 测试失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return False
    finally:
        # 清理测试文件
        try:
            if os.path.exists("game_progress.json"):
                os.remove("game_progress.json")
        except:
            pass
        
        pygame.quit()

def test_interactive_features():
    """交互式功能测试说明"""
    print("\n" + "=" * 60)
    print("🎮 交互式功能测试说明")
    print("=" * 60)
    print("以下功能需要手动测试：")
    print()
    print("1. 🎯 动态分数显示测试：")
    print("   - 启动游戏并开始游玩")
    print("   - 观察游戏界面底部的分数、生命、关卡等信息")
    print("   - 吃豆子时分数应实时更新")
    print()
    print("2. 🏠 返回首页功能测试：")
    print("   - 在游戏中按H键")
    print("   - 应该返回到开始界面")
    print("   - 在暂停界面也可以按H键返回首页")
    print()
    print("3. 💾 保存进度功能测试：")
    print("   - 开始游戏并获得一些分数")
    print("   - 按ESC暂停游戏")
    print("   - 按S键保存游戏进度")
    print("   - 按H键返回首页")
    print("   - 按C键继续游戏，应该恢复之前的进度")
    print()
    print("4. ⌨️  新按键功能测试：")
    print("   - H键：返回首页")
    print("   - S键：保存游戏（游戏中或暂停时）")
    print("   - C键：继续游戏（开始界面，如有存档）")
    print()
    print("🚀 运行游戏进行手动测试：python game.py")
    print("=" * 60)

if __name__ == "__main__":
    # 运行自动化测试
    success = run_all_tests()
    
    # 显示交互式测试说明
    test_interactive_features()
    
    if success:
        print("\n🎉 自动化测试全部通过！现在可以进行手动测试。")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，请检查代码。")
        sys.exit(1)
