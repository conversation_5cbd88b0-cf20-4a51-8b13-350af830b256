#!/bin/bash

# 吃豆子游戏启动脚本

echo "🎮 启动吃豆子游戏..."

# 检查Python是否安装
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "❌ 错误: 未找到Python，请先安装Python 3.6+"
    exit 1
fi

# 使用python3或python
PYTHON_CMD="python3"
if ! command -v python3 &> /dev/null; then
    PYTHON_CMD="python"
fi

# 检查pygame是否安装
if ! $PYTHON_CMD -c "import pygame" &> /dev/null; then
    echo "❌ 错误: pygame未安装"
    echo "请运行: pip install pygame"
    exit 1
fi

# 检查游戏文件是否存在
if [ ! -f "game.py" ]; then
    echo "❌ 错误: 未找到game.py文件"
    echo "请确保在游戏目录中运行此脚本"
    exit 1
fi

# 检查中文字体
echo "🔤 检查中文字体支持..."
if fc-list :lang=zh-cn | grep -q "Noto\|WenQuanYi\|AR PL" 2>/dev/null; then
    echo "✅ 中文字体支持正常"
else
    echo "⚠️  中文字体可能有问题，建议运行: ./install_fonts.sh"
fi

# 启动游戏
echo "🚀 启动游戏..."
$PYTHON_CMD game.py
