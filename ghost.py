import pygame
import random
import math

class Ghost:
    def __init__(self, x, y, grid_size, color=(255, 0, 0)):
        self.grid_x = x
        self.grid_y = y
        self.grid_size = grid_size
        self.pixel_x = x * grid_size + grid_size // 2
        self.pixel_y = y * grid_size + grid_size // 2
        
        # 移动相关
        self.direction = random.randint(0, 3)  # 随机初始方向
        self.speed = 1  # 默认速度，可以通过难度调整
        self.move_timer = 0
        self.move_delay = 3  # 移动延迟，让幽灵不会太快
        self.chase_probability = 0.3  # 追踪概率，可以通过难度调整
        
        # 颜色
        self.color = color
        self.body_color = color
        self.eye_color = (255, 255, 255)
        self.pupil_color = (0, 0, 0)
        
        # 方向向量
        self.directions = [
            (1, 0),   # 右
            (0, 1),   # 下
            (-1, 0),  # 左
            (0, -1)   # 上
        ]
        
        # AI行为
        self.target_x = x
        self.target_y = y
        self.behavior_timer = 0
        self.behavior_change_interval = 180  # 3秒改变一次行为（60fps）
        
    def can_move(self, maze, dx, dy):
        """检查是否可以移动到指定位置"""
        new_x = self.grid_x + dx
        new_y = self.grid_y + dy
        return not maze.is_wall(new_x, new_y)
    
    def get_valid_directions(self, maze):
        """获取所有可行的移动方向"""
        valid_directions = []
        for i, (dx, dy) in enumerate(self.directions):
            if self.can_move(maze, dx, dy):
                valid_directions.append(i)
        return valid_directions
    
    def choose_direction(self, maze, pacman_x, pacman_y):
        """选择移动方向（简单AI）"""
        valid_directions = self.get_valid_directions(maze)
        
        if not valid_directions:
            return self.direction
        
        # 根据难度设置的概率追踪吃豆人
        if random.random() < self.chase_probability:
            # 追踪模式：朝向吃豆人的方向
            dx_to_pacman = pacman_x - self.grid_x
            dy_to_pacman = pacman_y - self.grid_y
            
            best_direction = self.direction
            min_distance = float('inf')
            
            for direction in valid_directions:
                dx, dy = self.directions[direction]
                new_x = self.grid_x + dx
                new_y = self.grid_y + dy
                
                # 计算到吃豆人的距离
                distance = abs(new_x - pacman_x) + abs(new_y - pacman_y)
                if distance < min_distance:
                    min_distance = distance
                    best_direction = direction
            
            return best_direction
        else:
            # 随机移动，但避免立即掉头
            opposite_direction = (self.direction + 2) % 4
            preferred_directions = [d for d in valid_directions if d != opposite_direction]
            
            if preferred_directions:
                return random.choice(preferred_directions)
            else:
                return random.choice(valid_directions)
    
    def update(self, maze, pacman_x, pacman_y):
        """更新幽灵状态"""
        self.move_timer += 1
        self.behavior_timer += 1
        
        # 定期改变行为
        if self.behavior_timer >= self.behavior_change_interval:
            self.behavior_timer = 0
        
        # 移动逻辑
        if self.move_timer >= self.move_delay:
            self.move_timer = 0
            
            # 选择新方向
            new_direction = self.choose_direction(maze, pacman_x, pacman_y)
            self.direction = new_direction
            
            # 移动
            dx, dy = self.directions[self.direction]
            if self.can_move(maze, dx, dy):
                self.pixel_x += dx * self.speed
                self.pixel_y += dy * self.speed
                
                # 检查是否到达网格中心
                target_x = self.grid_x * self.grid_size + self.grid_size // 2
                target_y = self.grid_y * self.grid_size + self.grid_size // 2
                
                if dx > 0 and self.pixel_x >= target_x + self.grid_size:
                    self.grid_x += 1
                    self.pixel_x = self.grid_x * self.grid_size + self.grid_size // 2
                elif dx < 0 and self.pixel_x <= target_x - self.grid_size:
                    self.grid_x -= 1
                    self.pixel_x = self.grid_x * self.grid_size + self.grid_size // 2
                elif dy > 0 and self.pixel_y >= target_y + self.grid_size:
                    self.grid_y += 1
                    self.pixel_y = self.grid_y * self.grid_size + self.grid_size // 2
                elif dy < 0 and self.pixel_y <= target_y - self.grid_size:
                    self.grid_y -= 1
                    self.pixel_y = self.grid_y * self.grid_size + self.grid_size // 2
    
    def check_collision(self, pacman_x, pacman_y):
        """检查是否与吃豆人碰撞"""
        distance = math.sqrt((self.grid_x - pacman_x)**2 + (self.grid_y - pacman_y)**2)
        return distance < 1.0  # 在同一格子或相邻格子
    
    def draw(self, screen, offset_y=0):
        """绘制幽灵"""
        radius = self.grid_size // 2 - 2

        # 绘制幽灵身体（圆形上半部分 + 波浪形下半部分）
        center_x = int(self.pixel_x)
        center_y = int(self.pixel_y + offset_y)
        
        # 身体上半部分（半圆）
        pygame.draw.circle(screen, self.body_color, (center_x, center_y - 2), radius)
        
        # 身体下半部分（矩形）
        body_rect = pygame.Rect(center_x - radius, center_y - 2, radius * 2, radius + 2)
        pygame.draw.rect(screen, self.body_color, body_rect)
        
        # 底部波浪效果（简化为锯齿状）
        wave_points = []
        wave_width = radius * 2 // 4
        for i in range(5):
            x = center_x - radius + i * wave_width
            y = center_y + radius if i % 2 == 0 else center_y + radius - 4
            wave_points.append((x, y))
        
        if len(wave_points) > 2:
            pygame.draw.polygon(screen, self.body_color, wave_points)
        
        # 绘制眼睛
        eye_radius = 3
        left_eye_x = center_x - radius // 2
        right_eye_x = center_x + radius // 2
        eye_y = center_y - radius // 3
        
        # 眼白
        pygame.draw.circle(screen, self.eye_color, (left_eye_x, eye_y), eye_radius)
        pygame.draw.circle(screen, self.eye_color, (right_eye_x, eye_y), eye_radius)
        
        # 眼珠
        pupil_radius = 2
        pygame.draw.circle(screen, self.pupil_color, (left_eye_x, eye_y), pupil_radius)
        pygame.draw.circle(screen, self.pupil_color, (right_eye_x, eye_y), pupil_radius)
    
    def get_position(self):
        """获取当前网格位置"""
        return self.grid_x, self.grid_y
    
    def reset_position(self, x, y):
        """重置位置"""
        self.grid_x = x
        self.grid_y = y
        self.pixel_x = x * self.grid_size + self.grid_size // 2
        self.pixel_y = y * self.grid_size + self.grid_size // 2
