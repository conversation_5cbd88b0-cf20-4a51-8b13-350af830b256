import pygame
import sys
from pacman import Pacman
from maze import Maze
from ghost import Ghost

class PacmanGame:
    def __init__(self):
        # 初始化pygame
        pygame.init()
        
        # 游戏设置
        self.GRID_SIZE = 20
        self.MAZE_WIDTH = 40
        self.MAZE_HEIGHT = 34
        self.SCREEN_WIDTH = self.MAZE_WIDTH * self.GRID_SIZE
        self.SCREEN_HEIGHT = self.MAZE_HEIGHT * self.GRID_SIZE + 100  # 额外空间显示分数
        
        # 创建屏幕
        self.screen = pygame.display.set_mode((self.SCREEN_WIDTH, self.SCREEN_HEIGHT))
        pygame.display.set_caption("吃豆子游戏 - Pacman")
        
        # 游戏时钟
        self.clock = pygame.time.Clock()
        self.FPS = 60
        
        # 创建游戏对象
        self.maze = Maze(self.SCREEN_WIDTH, self.MAZE_HEIGHT * self.GRID_SIZE, self.GRID_SIZE)
        self.pacman = Pacman(1, 1, self.GRID_SIZE)  # 在迷宫中找一个合适的起始位置

        # 创建幽灵
        self.ghosts = []
        ghost_colors = [(255, 0, 0), (255, 192, 203), (0, 255, 255), (255, 165, 0)]  # 红、粉、青、橙
        self.create_ghosts(ghost_colors)

        # 游戏状态
        self.game_state = "START"  # START, PLAYING, PAUSED, GAME_OVER, WIN
        self.score = 0
        self.high_score = self.load_high_score()
        self.lives = 3  # 生命数
        self.level = 1
        self.game_over = False
        self.game_won = False
        self.running = True

        # 计时器
        self.game_time = 0
        self.pause_time = 0
        
        # 字体 - 支持中文
        self.font = self.get_chinese_font(36)
        self.big_font = self.get_chinese_font(72)
        self.small_font = self.get_chinese_font(24)
        
        # 颜色
        self.BLACK = (0, 0, 0)
        self.WHITE = (255, 255, 255)
        self.YELLOW = (255, 255, 0)
        self.RED = (255, 0, 0)
        self.GREEN = (0, 255, 0)
        
        # 找到一个合适的起始位置
        self.find_start_position()

    def get_chinese_font(self, size):
        """获取支持中文的字体"""
        # 常见的中文字体路径列表（按优先级排序）
        chinese_fonts = [
            # Linux 系统字体（优先使用系统中存在的字体）
            "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc",
            "/usr/share/fonts/opentype/noto/NotoSansCJK-Bold.ttc",
            "/usr/share/fonts/truetype/arphic/ukai.ttc",
            "/usr/share/fonts/truetype/arphic/uming.ttc",
            "/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc",
            "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            # Windows 系统字体
            "C:/Windows/Fonts/simhei.ttf",
            "C:/Windows/Fonts/simsun.ttc",
            "C:/Windows/Fonts/msyh.ttc",
            # macOS 系统字体
            "/System/Library/Fonts/PingFang.ttc",
            "/System/Library/Fonts/Hiragino Sans GB.ttc",
        ]

        # 尝试加载中文字体
        for font_path in chinese_fonts:
            try:
                return pygame.font.Font(font_path, size)
            except:
                continue

        # 如果没有找到中文字体，尝试系统默认字体
        try:
            # 尝试获取系统字体
            font = pygame.font.SysFont("simhei,simsun,microsoftyahei,pingfang,notosanscjk,wqyzenhei,wqymicrohei", size)
            if font:
                return font
        except:
            pass

        # 最后回退到默认字体
        print("警告: 未找到中文字体，可能无法正确显示中文")
        return pygame.font.Font(None, size)

    def load_high_score(self):
        """加载最高分"""
        try:
            with open("high_score.txt", "r") as f:
                return int(f.read().strip())
        except:
            return 0

    def save_high_score(self):
        """保存最高分"""
        try:
            with open("high_score.txt", "w") as f:
                f.write(str(self.high_score))
        except:
            pass

    def create_ghosts(self, colors):
        """创建幽灵"""
        # 在迷宫中找到合适的幽灵起始位置
        ghost_positions = [(19, 9), (20, 9), (19, 10), (20, 10)]  # 中央区域

        for i, color in enumerate(colors):
            if i < len(ghost_positions):
                x, y = ghost_positions[i]
                # 确保位置不是墙壁
                if not self.maze.is_wall(x, y):
                    ghost = Ghost(x, y, self.GRID_SIZE, color)
                    self.ghosts.append(ghost)

    def find_start_position(self):
        """找到一个合适的起始位置（非墙壁位置）"""
        for y in range(len(self.maze.maze_layout)):
            for x in range(len(self.maze.maze_layout[0])):
                if not self.maze.is_wall(x, y):
                    self.pacman.grid_x = x
                    self.pacman.grid_y = y
                    self.pacman.pixel_x = x * self.GRID_SIZE + self.GRID_SIZE // 2
                    self.pacman.pixel_y = y * self.GRID_SIZE + self.GRID_SIZE // 2
                    return
    
    def handle_events(self):
        """处理游戏事件"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    if self.game_state == "PLAYING":
                        self.game_state = "PAUSED"
                    elif self.game_state == "PAUSED":
                        self.game_state = "PLAYING"
                    else:
                        self.running = False

                elif event.key == pygame.K_SPACE:
                    if self.game_state == "START":
                        self.start_game()
                    elif self.game_state == "PAUSED":
                        self.game_state = "PLAYING"
                    elif self.game_state in ["GAME_OVER", "WIN"]:
                        self.restart_game()

                elif event.key == pygame.K_r:
                    if self.game_state in ["GAME_OVER", "WIN", "PLAYING"]:
                        self.restart_game()

                elif self.game_state == "PLAYING":
                    # 控制吃豆人移动
                    if event.key == pygame.K_RIGHT or event.key == pygame.K_d:
                        self.pacman.set_direction(0)  # 右
                    elif event.key == pygame.K_DOWN or event.key == pygame.K_s:
                        self.pacman.set_direction(1)  # 下
                    elif event.key == pygame.K_LEFT or event.key == pygame.K_a:
                        self.pacman.set_direction(2)  # 左
                    elif event.key == pygame.K_UP or event.key == pygame.K_w:
                        self.pacman.set_direction(3)  # 上
    
    def start_game(self):
        """开始游戏"""
        self.game_state = "PLAYING"
        self.game_time = 0

    def update(self):
        """更新游戏状态"""
        if self.game_state == "PLAYING":
            self.game_time += 1

            # 更新吃豆人
            self.pacman.update(self.maze)

            # 更新幽灵
            pacman_x, pacman_y = self.pacman.get_position()
            for ghost in self.ghosts:
                ghost.update(self.maze, pacman_x, pacman_y)

                # 检查碰撞
                if ghost.check_collision(pacman_x, pacman_y):
                    self.lives -= 1
                    if self.lives <= 0:
                        self.game_state = "GAME_OVER"
                        # 检查是否创造新纪录
                        if self.score > self.high_score:
                            self.high_score = self.score
                            self.save_high_score()
                    else:
                        # 重置所有角色位置
                        self.reset_positions()

            # 检查是否吃到豆子
            points = self.pacman.eat_dot(self.maze)
            if points > 0:
                self.score += points
                # 额外奖励分数
                if points == 50:  # 大豆子
                    self.score += 40  # 额外奖励

            # 检查是否获胜（所有豆子都被吃完）
            if self.maze.count_dots() == 0:
                self.game_state = "WIN"
                # 关卡奖励
                self.score += 1000 * self.level
                # 检查是否创造新纪录
                if self.score > self.high_score:
                    self.high_score = self.score
                    self.save_high_score()
    
    def draw(self):
        """绘制游戏画面"""
        # 清空屏幕
        self.screen.fill(self.BLACK)

        if self.game_state == "START":
            self.draw_start_screen()
        elif self.game_state == "PLAYING":
            self.draw_game_screen()
        elif self.game_state == "PAUSED":
            self.draw_game_screen()
            self.draw_pause_overlay()
        elif self.game_state == "GAME_OVER":
            self.draw_game_over_screen()
        elif self.game_state == "WIN":
            self.draw_win_screen()

        # 更新显示
        pygame.display.flip()

    def draw_start_screen(self):
        """绘制开始界面"""
        # 标题
        title_text = self.big_font.render("吃豆子游戏", True, self.YELLOW)
        title_rect = title_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 3))
        self.screen.blit(title_text, title_rect)

        # 副标题
        subtitle_text = self.font.render("PACMAN GAME", True, self.WHITE)
        subtitle_rect = subtitle_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 3 + 60))
        self.screen.blit(subtitle_text, subtitle_rect)

        # 最高分
        high_score_text = self.font.render(f"最高分: {self.high_score}", True, self.GREEN)
        high_score_rect = high_score_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2))
        self.screen.blit(high_score_text, high_score_rect)

        # 开始提示
        start_text = self.font.render("按空格键开始游戏", True, self.WHITE)
        start_rect = start_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 60))
        self.screen.blit(start_text, start_rect)

        # 控制说明
        controls = [
            "游戏控制:",
            "方向键或WASD - 移动",
            "ESC - 暂停/继续",
            "R - 重新开始",
            "空格 - 开始/继续"
        ]

        for i, control in enumerate(controls):
            color = self.YELLOW if i == 0 else self.WHITE
            control_text = self.font.render(control, True, color)
            control_rect = control_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 120 + i * 30))
            self.screen.blit(control_text, control_rect)

    def draw_game_screen(self):
        """绘制游戏界面"""
        # 绘制迷宫
        self.maze.draw(self.screen)

        # 绘制幽灵
        for ghost in self.ghosts:
            ghost.draw(self.screen)

        # 绘制吃豆人
        self.pacman.draw(self.screen)

        # 绘制游戏信息
        self.draw_game_info()

    def draw_game_info(self):
        """绘制游戏信息栏"""
        info_y = self.MAZE_HEIGHT * self.GRID_SIZE + 10

        # 分数
        score_text = self.font.render(f"分数: {self.score}", True, self.YELLOW)
        self.screen.blit(score_text, (10, info_y))

        # 最高分
        high_score_text = self.font.render(f"最高分: {self.high_score}", True, self.GREEN)
        self.screen.blit(high_score_text, (200, info_y))

        # 生命数
        lives_text = self.font.render(f"生命: {self.lives}", True, self.RED)
        self.screen.blit(lives_text, (400, info_y))

        # 关卡
        level_text = self.font.render(f"关卡: {self.level}", True, self.WHITE)
        self.screen.blit(level_text, (550, info_y))

        # 剩余豆子数量
        dots_left = self.maze.count_dots()
        dots_text = self.font.render(f"剩余豆子: {dots_left}", True, self.WHITE)
        self.screen.blit(dots_text, (10, info_y + 40))

        # 游戏时间
        game_time_seconds = self.game_time // 60
        time_text = self.font.render(f"时间: {game_time_seconds}s", True, self.WHITE)
        self.screen.blit(time_text, (200, info_y + 40))

    def draw_pause_overlay(self):
        """绘制暂停覆盖层"""
        # 半透明背景
        overlay = pygame.Surface((self.SCREEN_WIDTH, self.SCREEN_HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(self.BLACK)
        self.screen.blit(overlay, (0, 0))

        # 暂停文字
        pause_text = self.big_font.render("游戏暂停", True, self.WHITE)
        pause_rect = pause_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2))
        self.screen.blit(pause_text, pause_rect)

        # 继续提示
        continue_text = self.font.render("按ESC或空格键继续", True, self.WHITE)
        continue_rect = continue_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 50))
        self.screen.blit(continue_text, continue_rect)

    def draw_game_over_screen(self):
        """绘制游戏结束界面"""
        # 绘制游戏界面作为背景
        self.draw_game_screen()

        # 半透明覆盖层
        overlay = pygame.Surface((self.SCREEN_WIDTH, self.SCREEN_HEIGHT))
        overlay.set_alpha(180)
        overlay.fill(self.BLACK)
        self.screen.blit(overlay, (0, 0))

        # 游戏结束文字
        game_over_text = self.big_font.render("游戏结束", True, self.RED)
        game_over_rect = game_over_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 - 80))
        self.screen.blit(game_over_text, game_over_rect)

        # 最终分数
        final_score_text = self.font.render(f"最终分数: {self.score}", True, self.YELLOW)
        final_score_rect = final_score_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 - 20))
        self.screen.blit(final_score_text, final_score_rect)

        # 最高分
        if self.score == self.high_score and self.score > 0:
            new_record_text = self.font.render("新纪录！", True, self.GREEN)
            new_record_rect = new_record_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 20))
            self.screen.blit(new_record_text, new_record_rect)

        high_score_text = self.font.render(f"最高分: {self.high_score}", True, self.GREEN)
        high_score_rect = high_score_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 50))
        self.screen.blit(high_score_text, high_score_rect)

        # 重新开始提示
        restart_text = self.font.render("按空格键或R键重新开始", True, self.WHITE)
        restart_rect = restart_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 100))
        self.screen.blit(restart_text, restart_rect)

    def draw_win_screen(self):
        """绘制获胜界面"""
        # 绘制游戏界面作为背景
        self.draw_game_screen()

        # 半透明覆盖层
        overlay = pygame.Surface((self.SCREEN_WIDTH, self.SCREEN_HEIGHT))
        overlay.set_alpha(180)
        overlay.fill(self.BLACK)
        self.screen.blit(overlay, (0, 0))

        # 获胜文字
        win_text = self.big_font.render("恭喜获胜！", True, self.GREEN)
        win_rect = win_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 - 80))
        self.screen.blit(win_text, win_rect)

        # 关卡完成
        level_text = self.font.render(f"关卡 {self.level} 完成！", True, self.YELLOW)
        level_rect = level_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 - 40))
        self.screen.blit(level_text, level_rect)

        # 最终分数
        final_score_text = self.font.render(f"最终分数: {self.score}", True, self.YELLOW)
        final_score_rect = final_score_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2))
        self.screen.blit(final_score_text, final_score_rect)

        # 奖励分数
        bonus_text = self.font.render(f"关卡奖励: {1000 * self.level}", True, self.WHITE)
        bonus_rect = bonus_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 30))
        self.screen.blit(bonus_text, bonus_rect)

        # 最高分
        if self.score == self.high_score:
            new_record_text = self.font.render("新纪录！", True, self.GREEN)
            new_record_rect = new_record_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 60))
            self.screen.blit(new_record_text, new_record_rect)

        high_score_text = self.font.render(f"最高分: {self.high_score}", True, self.GREEN)
        high_score_rect = high_score_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 90))
        self.screen.blit(high_score_text, high_score_rect)

        # 继续提示
        continue_text = self.font.render("按空格键或R键重新开始", True, self.WHITE)
        continue_rect = continue_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 130))
        self.screen.blit(continue_text, continue_rect)
    
    def restart_game(self):
        """重新开始游戏"""
        # 重新创建迷宫和吃豆人
        self.maze = Maze(self.SCREEN_WIDTH, self.MAZE_HEIGHT * self.GRID_SIZE, self.GRID_SIZE)
        self.pacman = Pacman(1, 1, self.GRID_SIZE)
        self.find_start_position()

        # 重新创建幽灵
        self.ghosts = []
        ghost_colors = [(255, 0, 0), (255, 192, 203), (0, 255, 255), (255, 165, 0)]
        self.create_ghosts(ghost_colors)

        # 重置游戏状态
        self.game_state = "PLAYING"
        self.score = 0
        self.lives = 3
        self.level = 1
        self.game_time = 0
        self.game_over = False
        self.game_won = False

    def reset_positions(self):
        """重置所有角色位置（死亡后复活用）"""
        self.find_start_position()
        # 重置幽灵位置
        ghost_positions = [(19, 9), (20, 9), (19, 10), (20, 10)]
        for i, ghost in enumerate(self.ghosts):
            if i < len(ghost_positions):
                x, y = ghost_positions[i]
                ghost.reset_position(x, y)
    
    def run(self):
        """运行游戏主循环"""
        print("=" * 50)
        print("🎮 吃豆子游戏启动！")
        print("=" * 50)
        print("🎯 游戏目标：吃掉所有豆子获胜")
        print("🕹️  控制说明：")
        print("   - 方向键或WASD：移动吃豆人")
        print("   - 空格键：开始游戏/继续游戏")
        print("   - ESC键：暂停/继续游戏")
        print("   - R键：重新开始游戏")
        print("🏆 计分规则：")
        print("   - 小豆子：10分")
        print("   - 大豆子：50分 + 40分奖励")
        print("   - 关卡完成：1000分 × 关卡数")
        print("=" * 50)

        while self.running:
            # 处理事件
            self.handle_events()

            # 更新游戏状态
            self.update()

            # 绘制画面
            self.draw()

            # 控制帧率
            self.clock.tick(self.FPS)

        # 保存最高分
        self.save_high_score()

        # 退出游戏
        print("🎮 感谢游玩！")
        print(f"🏆 最终分数：{self.score}")
        print(f"🥇 最高纪录：{self.high_score}")
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = PacmanGame()
    game.run()
