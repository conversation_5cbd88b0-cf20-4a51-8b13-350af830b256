import pygame
import sys
from pacman import <PERSON><PERSON>
from maze import Maze
from ghost import Ghost

class PacmanGame:
    def __init__(self):
        # 初始化pygame
        pygame.init()
        
        # 游戏设置
        self.GRID_SIZE = 20
        self.MAZE_WIDTH = 40
        self.MAZE_HEIGHT = 34
        self.SCREEN_WIDTH = self.MAZE_WIDTH * self.GRID_SIZE
        self.INFO_BAR_HEIGHT = 80  # 顶部信息栏高度
        self.SCREEN_HEIGHT = self.MAZE_HEIGHT * self.GRID_SIZE + self.INFO_BAR_HEIGHT  # 顶部信息栏空间
        
        # 创建屏幕
        self.screen = pygame.display.set_mode((self.SCREEN_WIDTH, self.SCREEN_HEIGHT))
        pygame.display.set_caption("吃豆子游戏 - Pacman")
        
        # 游戏时钟
        self.clock = pygame.time.Clock()
        self.FPS = 60
        
        # 创建游戏对象
        self.maze = Maze(self.SCREEN_WIDTH, self.MAZE_HEIGHT * self.GRID_SIZE, self.GRID_SIZE)
        self.pacman = Pacman(1, 1, self.GRID_SIZE)  # 在迷宫中找一个合适的起始位置

        # 创建幽灵（在难度设置之后）
        self.ghosts = []

        # 难度设置
        self.difficulty_levels = {
            "EASY": {
                "name": "简单",
                "description": "适合新手玩家",
                "ghost_speed": 1,
                "ghost_count": 2,
                "chase_probability": 0.2,
                "lives": 5,
                "score_multiplier": 1.0
            },
            "NORMAL": {
                "name": "普通",
                "description": "标准游戏体验",
                "ghost_speed": 1,
                "ghost_count": 4,
                "chase_probability": 0.3,
                "lives": 3,
                "score_multiplier": 1.0
            },
            "HARD": {
                "name": "困难",
                "description": "挑战你的技巧",
                "ghost_speed": 2,
                "ghost_count": 4,
                "chase_probability": 0.5,
                "lives": 2,
                "score_multiplier": 1.5
            },
            "EXPERT": {
                "name": "专家",
                "description": "终极挑战",
                "ghost_speed": 2,
                "ghost_count": 6,
                "chase_probability": 0.7,
                "lives": 1,
                "score_multiplier": 2.0
            }
        }
        self.current_difficulty = "NORMAL"
        self.selected_difficulty_index = 1  # 默认选择普通难度

        # 游戏状态
        self.game_state = "START"  # START, PLAYING, PAUSED, GAME_OVER, WIN
        self.score = 0
        self.high_score = self.load_high_score()
        self.lives = self.difficulty_levels[self.current_difficulty]["lives"]
        self.level = 1
        self.game_over = False
        self.game_won = False
        self.running = True

        # 计时器
        self.game_time = 0
        self.pause_time = 0
        
        # 字体 - 支持中文
        self.font = self.get_chinese_font(36)
        self.big_font = self.get_chinese_font(72)
        self.small_font = self.get_chinese_font(24)
        
        # 颜色
        self.BLACK = (0, 0, 0)
        self.WHITE = (255, 255, 255)
        self.YELLOW = (255, 255, 0)
        self.RED = (255, 0, 0)
        self.GREEN = (0, 255, 0)
        
        # 找到一个合适的起始位置
        self.find_start_position()

        # 根据默认难度创建幽灵
        self.create_ghosts()

    def get_chinese_font(self, size):
        """获取支持中文的字体"""
        # 常见的中文字体路径列表（按优先级排序）
        chinese_fonts = [
            # Linux 系统字体（优先使用系统中存在的字体）
            "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc",
            "/usr/share/fonts/opentype/noto/NotoSansCJK-Bold.ttc",
            "/usr/share/fonts/truetype/arphic/ukai.ttc",
            "/usr/share/fonts/truetype/arphic/uming.ttc",
            "/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc",
            "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            # Windows 系统字体
            "C:/Windows/Fonts/simhei.ttf",
            "C:/Windows/Fonts/simsun.ttc",
            "C:/Windows/Fonts/msyh.ttc",
            # macOS 系统字体
            "/System/Library/Fonts/PingFang.ttc",
            "/System/Library/Fonts/Hiragino Sans GB.ttc",
        ]

        # 尝试加载中文字体
        for font_path in chinese_fonts:
            try:
                return pygame.font.Font(font_path, size)
            except:
                continue

        # 如果没有找到中文字体，尝试系统默认字体
        try:
            # 尝试获取系统字体
            font = pygame.font.SysFont("simhei,simsun,microsoftyahei,pingfang,notosanscjk,wqyzenhei,wqymicrohei", size)
            if font:
                return font
        except:
            pass

        # 最后回退到默认字体
        print("警告: 未找到中文字体，可能无法正确显示中文")
        return pygame.font.Font(None, size)

    def load_high_score(self):
        """加载最高分"""
        try:
            with open("high_score.txt", "r") as f:
                return int(f.read().strip())
        except:
            return 0

    def save_high_score(self):
        """保存最高分"""
        try:
            with open("high_score.txt", "w") as f:
                f.write(str(self.high_score))
        except:
            pass

    def save_game_progress(self):
        """保存游戏进度"""
        try:
            import json
            progress_data = {
                "score": self.score,
                "lives": self.lives,
                "level": self.level,
                "game_time": self.game_time,
                "pacman_position": [self.pacman.grid_x, self.pacman.grid_y],
                "maze_state": self.maze.maze_layout,
                "has_saved_game": True
            }

            with open("game_progress.json", "w") as f:
                json.dump(progress_data, f, indent=2)
            print("✅ 游戏进度已保存")
        except Exception as e:
            print(f"❌ 保存进度失败: {e}")

    def load_game_progress(self):
        """加载游戏进度"""
        try:
            import json
            with open("game_progress.json", "r") as f:
                progress_data = json.load(f)

            if progress_data.get("has_saved_game", False):
                self.score = progress_data.get("score", 0)
                self.lives = progress_data.get("lives", 3)
                self.level = progress_data.get("level", 1)
                self.game_time = progress_data.get("game_time", 0)

                # 恢复吃豆人位置
                pacman_pos = progress_data.get("pacman_position", [1, 1])
                self.pacman.grid_x = pacman_pos[0]
                self.pacman.grid_y = pacman_pos[1]
                self.pacman.pixel_x = pacman_pos[0] * self.GRID_SIZE + self.GRID_SIZE // 2
                self.pacman.pixel_y = pacman_pos[1] * self.GRID_SIZE + self.GRID_SIZE // 2

                # 恢复迷宫状态
                maze_state = progress_data.get("maze_state")
                if maze_state:
                    self.maze.maze_layout = maze_state

                print("✅ 游戏进度已加载")
                return True
        except Exception as e:
            print(f"❌ 加载进度失败: {e}")

        return False

    def has_saved_game(self):
        """检查是否有保存的游戏"""
        try:
            import json
            with open("game_progress.json", "r") as f:
                progress_data = json.load(f)
            return progress_data.get("has_saved_game", False)
        except:
            return False

    def delete_saved_game(self):
        """删除保存的游戏"""
        try:
            import os
            if os.path.exists("game_progress.json"):
                os.remove("game_progress.json")
                print("✅ 已删除保存的游戏")
        except Exception as e:
            print(f"❌ 删除保存游戏失败: {e}")

    def create_ghosts(self):
        """根据难度创建幽灵"""
        difficulty = self.difficulty_levels[self.current_difficulty]
        ghost_count = difficulty["ghost_count"]
        ghost_speed = difficulty["ghost_speed"]
        chase_prob = difficulty["chase_probability"]

        # 幽灵颜色列表
        ghost_colors = [
            (255, 0, 0),     # 红色
            (255, 192, 203), # 粉色
            (0, 255, 255),   # 青色
            (255, 165, 0),   # 橙色
            (128, 0, 128),   # 紫色
            (0, 255, 0)      # 绿色
        ]

        # 在迷宫中找到合适的幽灵起始位置
        ghost_positions = [
            (19, 9), (20, 9), (19, 10), (20, 10),  # 中央区域
            (18, 9), (21, 9)  # 额外位置
        ]

        self.ghosts = []  # 清空现有幽灵

        for i in range(min(ghost_count, len(ghost_positions), len(ghost_colors))):
            x, y = ghost_positions[i]
            color = ghost_colors[i]

            # 确保位置不是墙壁
            if not self.maze.is_wall(x, y):
                ghost = Ghost(x, y, self.GRID_SIZE, color)
                ghost.speed = ghost_speed
                ghost.chase_probability = chase_prob
                self.ghosts.append(ghost)

    def find_start_position(self):
        """找到一个合适的起始位置（非墙壁位置）"""
        for y in range(len(self.maze.maze_layout)):
            for x in range(len(self.maze.maze_layout[0])):
                if not self.maze.is_wall(x, y):
                    self.pacman.grid_x = x
                    self.pacman.grid_y = y
                    self.pacman.pixel_x = x * self.GRID_SIZE + self.GRID_SIZE // 2
                    # 注意：pixel_y不需要加偏移，因为绘制时会自动加偏移
                    self.pacman.pixel_y = y * self.GRID_SIZE + self.GRID_SIZE // 2
                    return
    
    def handle_events(self):
        """处理游戏事件"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    if self.game_state == "PLAYING":
                        self.game_state = "PAUSED"
                    elif self.game_state == "PAUSED":
                        self.game_state = "PLAYING"
                    else:
                        self.running = False

                elif event.key == pygame.K_SPACE or event.key == pygame.K_RETURN:
                    if self.game_state == "START":
                        self.start_game()
                    elif self.game_state == "PAUSED":
                        self.game_state = "PLAYING"
                    elif self.game_state in ["GAME_OVER", "WIN"]:
                        self.restart_game()

                elif event.key == pygame.K_c:
                    # 继续游戏功能
                    if self.game_state == "START" and self.has_saved_game():
                        self.continue_game()

                elif event.key == pygame.K_r:
                    if self.game_state in ["GAME_OVER", "WIN", "PLAYING"]:
                        self.restart_game()

                elif event.key == pygame.K_h:
                    # 返回首页功能
                    if self.game_state in ["PLAYING", "PAUSED", "GAME_OVER", "WIN"]:
                        self.return_to_home()

                elif event.key == pygame.K_s:
                    # 保存游戏功能（只在游戏中和暂停时，避免与移动冲突）
                    if self.game_state in ["PLAYING", "PAUSED"]:
                        self.save_game_progress()

                # 根据游戏状态处理特定按键
                if self.game_state == "START":
                    # 开始界面：难度选择
                    if event.key == pygame.K_UP:
                        self.selected_difficulty_index = (self.selected_difficulty_index - 1) % len(self.difficulty_levels)
                        self.update_current_difficulty()
                    elif event.key == pygame.K_DOWN:
                        self.selected_difficulty_index = (self.selected_difficulty_index + 1) % len(self.difficulty_levels)
                        self.update_current_difficulty()

                elif self.game_state == "PLAYING":
                    # 游戏中：控制吃豆人移动
                    if event.key == pygame.K_RIGHT or event.key == pygame.K_d:
                        self.pacman.set_direction(0)  # 右
                    elif event.key == pygame.K_DOWN or event.key == pygame.K_s:
                        self.pacman.set_direction(1)  # 下
                    elif event.key == pygame.K_LEFT or event.key == pygame.K_a:
                        self.pacman.set_direction(2)  # 左
                    elif event.key == pygame.K_UP or event.key == pygame.K_w:
                        self.pacman.set_direction(3)  # 上
    
    def start_game(self):
        """开始游戏"""
        # 根据选择的难度设置游戏参数
        difficulty = self.difficulty_levels[self.current_difficulty]
        self.lives = difficulty["lives"]

        # 重新创建幽灵（根据难度）
        self.create_ghosts()

        self.game_state = "PLAYING"
        self.game_time = 0
        # 删除之前的存档（开始新游戏）
        self.delete_saved_game()

        print(f"🎮 开始游戏 - 难度: {difficulty['name']}")
        print(f"   幽灵数量: {difficulty['ghost_count']}")
        print(f"   幽灵速度: {difficulty['ghost_speed']}x")
        print(f"   生命数: {difficulty['lives']}")
        print(f"   分数倍率: {difficulty['score_multiplier']}x")

    def continue_game(self):
        """继续游戏"""
        if self.load_game_progress():
            self.game_state = "PLAYING"
            print("🎮 继续之前的游戏...")
        else:
            print("❌ 无法加载游戏进度，开始新游戏")
            self.start_game()

    def update_current_difficulty(self):
        """更新当前选择的难度"""
        difficulty_keys = list(self.difficulty_levels.keys())
        self.current_difficulty = difficulty_keys[self.selected_difficulty_index]
        difficulty = self.difficulty_levels[self.current_difficulty]
        print(f"🎯 选择难度: {difficulty['name']} - {difficulty['description']}")

    def update(self):
        """更新游戏状态"""
        if self.game_state == "PLAYING":
            self.game_time += 1

            # 更新吃豆人
            self.pacman.update(self.maze)

            # 更新幽灵
            pacman_x, pacman_y = self.pacman.get_position()
            for ghost in self.ghosts:
                ghost.update(self.maze, pacman_x, pacman_y)

                # 检查碰撞
                if ghost.check_collision(pacman_x, pacman_y):
                    self.lives -= 1
                    if self.lives <= 0:
                        self.game_state = "GAME_OVER"
                        # 检查是否创造新纪录
                        if self.score > self.high_score:
                            self.high_score = self.score
                            self.save_high_score()
                        # 游戏结束时删除存档
                        self.delete_saved_game()
                    else:
                        # 重置所有角色位置
                        self.reset_positions()

            # 检查是否吃到豆子
            points = self.pacman.eat_dot(self.maze)
            if points > 0:
                # 应用难度分数倍率
                difficulty = self.difficulty_levels[self.current_difficulty]
                multiplied_points = int(points * difficulty["score_multiplier"])
                self.score += multiplied_points

                # 额外奖励分数
                if points == 50:  # 大豆子
                    bonus_points = int(40 * difficulty["score_multiplier"])
                    self.score += bonus_points

            # 检查是否获胜（所有豆子都被吃完）
            if self.maze.count_dots() == 0:
                self.game_state = "WIN"
                # 关卡奖励（考虑难度倍率）
                difficulty = self.difficulty_levels[self.current_difficulty]
                level_bonus = int(1000 * self.level * difficulty["score_multiplier"])
                self.score += level_bonus
                # 检查是否创造新纪录
                if self.score > self.high_score:
                    self.high_score = self.score
                    self.save_high_score()
                # 获胜时删除存档
                self.delete_saved_game()
    
    def draw(self):
        """绘制游戏画面"""
        # 清空屏幕
        self.screen.fill(self.BLACK)

        if self.game_state == "START":
            self.draw_start_screen()
        elif self.game_state == "PLAYING":
            self.draw_game_screen()
        elif self.game_state == "PAUSED":
            self.draw_game_screen()
            self.draw_pause_overlay()
        elif self.game_state == "GAME_OVER":
            self.draw_game_over_screen()
        elif self.game_state == "WIN":
            self.draw_win_screen()

        # 更新显示
        pygame.display.flip()

    def draw_start_screen(self):
        """绘制开始界面"""
        # 标题
        title_text = self.big_font.render("吃豆子游戏", True, self.YELLOW)
        title_rect = title_text.get_rect(center=(self.SCREEN_WIDTH // 2, 80))
        self.screen.blit(title_text, title_rect)

        # 副标题
        subtitle_text = self.font.render("PACMAN GAME", True, self.WHITE)
        subtitle_rect = subtitle_text.get_rect(center=(self.SCREEN_WIDTH // 2, 140))
        self.screen.blit(subtitle_text, subtitle_rect)

        # 最高分
        high_score_text = self.font.render(f"最高分: {self.high_score}", True, self.GREEN)
        high_score_rect = high_score_text.get_rect(center=(self.SCREEN_WIDTH // 2, 180))
        self.screen.blit(high_score_text, high_score_rect)

        # 难度选择
        self.draw_difficulty_selection()

        # 开始提示
        if self.has_saved_game():
            start_text = self.font.render("按空格键开始新游戏", True, self.WHITE)
            continue_text = self.font.render("按C键继续游戏", True, self.GREEN)
            start_rect = start_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 60))
            continue_rect = continue_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 90))
            self.screen.blit(start_text, start_rect)
            self.screen.blit(continue_text, continue_rect)
        else:
            start_text = self.font.render("按空格键开始游戏", True, self.WHITE)
            start_rect = start_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 60))
            self.screen.blit(start_text, start_rect)

        # 控制说明
        controls = [
            "游戏控制:",
            "方向键或WASD - 移动",
            "↑↓键 - 选择难度",
            "回车/空格 - 开始游戏",
            "ESC - 暂停/继续",
            "R - 重新开始",
            "H - 返回首页",
            "C - 继续游戏(如有存档)"
        ]

        for i, control in enumerate(controls):
            color = self.YELLOW if i == 0 else self.WHITE
            control_text = self.font.render(control, True, color)
            control_rect = control_text.get_rect(center=(self.SCREEN_WIDTH // 2, 520 + i * 30))
            self.screen.blit(control_text, control_rect)

    def draw_difficulty_selection(self):
        """绘制难度选择菜单"""
        # 难度选择标题
        difficulty_title = self.font.render("选择难度:", True, self.YELLOW)
        difficulty_title_rect = difficulty_title.get_rect(center=(self.SCREEN_WIDTH // 2, 240))
        self.screen.blit(difficulty_title, difficulty_title_rect)

        # 难度选择提示
        select_hint = self.small_font.render("使用↑↓键选择，回车确认", True, (200, 200, 200))
        select_hint_rect = select_hint.get_rect(center=(self.SCREEN_WIDTH // 2, 270))
        self.screen.blit(select_hint, select_hint_rect)

        # 绘制难度选项
        difficulty_keys = list(self.difficulty_levels.keys())
        start_y = 310

        for i, difficulty_key in enumerate(difficulty_keys):
            difficulty = self.difficulty_levels[difficulty_key]

            # 选中状态的背景
            if i == self.selected_difficulty_index:
                # 绘制选中背景
                bg_rect = pygame.Rect(self.SCREEN_WIDTH // 2 - 200, start_y + i * 60 - 5, 400, 50)
                pygame.draw.rect(self.screen, (50, 50, 100), bg_rect)
                pygame.draw.rect(self.screen, self.YELLOW, bg_rect, 2)
                text_color = self.YELLOW
                desc_color = self.WHITE
            else:
                text_color = self.WHITE
                desc_color = (150, 150, 150)

            # 难度名称
            name_text = self.font.render(f"{i+1}. {difficulty['name']}", True, text_color)
            name_rect = name_text.get_rect(center=(self.SCREEN_WIDTH // 2, start_y + i * 60))
            self.screen.blit(name_text, name_rect)

            # 难度描述
            desc_text = self.small_font.render(difficulty['description'], True, desc_color)
            desc_rect = desc_text.get_rect(center=(self.SCREEN_WIDTH // 2, start_y + i * 60 + 20))
            self.screen.blit(desc_text, desc_rect)

            # 难度参数（仅显示选中的）
            if i == self.selected_difficulty_index:
                params = f"幽灵: {difficulty['ghost_count']}个 | 速度: {difficulty['ghost_speed']}x | 生命: {difficulty['lives']}条"
                params_text = self.small_font.render(params, True, (200, 200, 200))
                params_rect = params_text.get_rect(center=(self.SCREEN_WIDTH // 2, start_y + i * 60 + 35))
                self.screen.blit(params_text, params_rect)

    def draw_game_screen(self):
        """绘制游戏界面"""
        # 先绘制顶部信息栏
        self.draw_game_info()

        # 绘制迷宫（向下偏移信息栏高度）
        self.maze.draw(self.screen, self.INFO_BAR_HEIGHT)

        # 绘制幽灵
        for ghost in self.ghosts:
            ghost.draw(self.screen, self.INFO_BAR_HEIGHT)

        # 绘制吃豆人
        self.pacman.draw(self.screen, self.INFO_BAR_HEIGHT)

    def draw_game_info(self):
        """绘制顶部游戏信息栏"""
        # 绘制信息栏背景
        info_rect = pygame.Rect(0, 0, self.SCREEN_WIDTH, self.INFO_BAR_HEIGHT)
        pygame.draw.rect(self.screen, (20, 20, 40), info_rect)  # 深蓝色背景
        pygame.draw.line(self.screen, self.WHITE, (0, self.INFO_BAR_HEIGHT-1), (self.SCREEN_WIDTH, self.INFO_BAR_HEIGHT-1), 2)

        # 第一行信息
        info_y1 = 10

        # 分数
        score_text = self.font.render(f"分数: {self.score}", True, self.YELLOW)
        self.screen.blit(score_text, (10, info_y1))

        # 最高分
        high_score_text = self.font.render(f"最高分: {self.high_score}", True, self.GREEN)
        self.screen.blit(high_score_text, (180, info_y1))

        # 生命数
        lives_text = self.font.render(f"生命: {self.lives}", True, self.RED)
        self.screen.blit(lives_text, (380, info_y1))

        # 关卡
        level_text = self.font.render(f"关卡: {self.level}", True, self.WHITE)
        self.screen.blit(level_text, (500, info_y1))

        # 难度
        difficulty = self.difficulty_levels[self.current_difficulty]
        difficulty_text = self.font.render(f"难度: {difficulty['name']}", True, self.YELLOW)
        self.screen.blit(difficulty_text, (620, info_y1))

        # 第二行信息
        info_y2 = 45

        # 剩余豆子数量
        dots_left = self.maze.count_dots()
        dots_text = self.small_font.render(f"剩余豆子: {dots_left}", True, self.WHITE)
        self.screen.blit(dots_text, (10, info_y2))

        # 游戏时间
        game_time_seconds = self.game_time // 60
        time_text = self.small_font.render(f"时间: {game_time_seconds}s", True, self.WHITE)
        self.screen.blit(time_text, (150, info_y2))

        # 控制提示
        control_text = self.small_font.render("H-返回首页 | ESC-暂停 | S-保存 | R-重新开始", True, (200, 200, 200))
        self.screen.blit(control_text, (280, info_y2))

    def draw_pause_overlay(self):
        """绘制暂停覆盖层"""
        # 半透明背景
        overlay = pygame.Surface((self.SCREEN_WIDTH, self.SCREEN_HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(self.BLACK)
        self.screen.blit(overlay, (0, 0))

        # 暂停文字
        pause_text = self.big_font.render("游戏暂停", True, self.WHITE)
        pause_rect = pause_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2))
        self.screen.blit(pause_text, pause_rect)

        # 继续提示
        continue_text = self.font.render("按ESC或空格键继续", True, self.WHITE)
        continue_rect = continue_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 50))
        self.screen.blit(continue_text, continue_rect)

        # 保存游戏提示
        save_text = self.font.render("按S键保存游戏", True, self.GREEN)
        save_rect = save_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 80))
        self.screen.blit(save_text, save_rect)

        # 返回首页提示
        home_text = self.font.render("按H键返回首页", True, self.YELLOW)
        home_rect = home_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 110))
        self.screen.blit(home_text, home_rect)

    def draw_game_over_screen(self):
        """绘制游戏结束界面"""
        # 绘制游戏界面作为背景
        self.draw_game_screen()

        # 半透明覆盖层
        overlay = pygame.Surface((self.SCREEN_WIDTH, self.SCREEN_HEIGHT))
        overlay.set_alpha(180)
        overlay.fill(self.BLACK)
        self.screen.blit(overlay, (0, 0))

        # 游戏结束文字
        game_over_text = self.big_font.render("游戏结束", True, self.RED)
        game_over_rect = game_over_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 - 80))
        self.screen.blit(game_over_text, game_over_rect)

        # 最终分数
        final_score_text = self.font.render(f"最终分数: {self.score}", True, self.YELLOW)
        final_score_rect = final_score_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 - 20))
        self.screen.blit(final_score_text, final_score_rect)

        # 最高分
        if self.score == self.high_score and self.score > 0:
            new_record_text = self.font.render("新纪录！", True, self.GREEN)
            new_record_rect = new_record_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 20))
            self.screen.blit(new_record_text, new_record_rect)

        high_score_text = self.font.render(f"最高分: {self.high_score}", True, self.GREEN)
        high_score_rect = high_score_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 50))
        self.screen.blit(high_score_text, high_score_rect)

        # 重新开始提示
        restart_text = self.font.render("按空格键或R键重新开始", True, self.WHITE)
        restart_rect = restart_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 100))
        self.screen.blit(restart_text, restart_rect)

    def draw_win_screen(self):
        """绘制获胜界面"""
        # 绘制游戏界面作为背景
        self.draw_game_screen()

        # 半透明覆盖层
        overlay = pygame.Surface((self.SCREEN_WIDTH, self.SCREEN_HEIGHT))
        overlay.set_alpha(180)
        overlay.fill(self.BLACK)
        self.screen.blit(overlay, (0, 0))

        # 获胜文字
        win_text = self.big_font.render("恭喜获胜！", True, self.GREEN)
        win_rect = win_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 - 80))
        self.screen.blit(win_text, win_rect)

        # 关卡完成
        difficulty = self.difficulty_levels[self.current_difficulty]
        level_text = self.font.render(f"关卡 {self.level} 完成！({difficulty['name']}难度)", True, self.YELLOW)
        level_rect = level_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 - 40))
        self.screen.blit(level_text, level_rect)

        # 最终分数
        final_score_text = self.font.render(f"最终分数: {self.score}", True, self.YELLOW)
        final_score_rect = final_score_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2))
        self.screen.blit(final_score_text, final_score_rect)

        # 奖励分数
        difficulty = self.difficulty_levels[self.current_difficulty]
        bonus_amount = int(1000 * self.level * difficulty["score_multiplier"])
        bonus_text = self.font.render(f"关卡奖励: {bonus_amount} (x{difficulty['score_multiplier']})", True, self.WHITE)
        bonus_rect = bonus_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 30))
        self.screen.blit(bonus_text, bonus_rect)

        # 最高分
        if self.score == self.high_score:
            new_record_text = self.font.render("新纪录！", True, self.GREEN)
            new_record_rect = new_record_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 60))
            self.screen.blit(new_record_text, new_record_rect)

        high_score_text = self.font.render(f"最高分: {self.high_score}", True, self.GREEN)
        high_score_rect = high_score_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 90))
        self.screen.blit(high_score_text, high_score_rect)

        # 继续提示
        continue_text = self.font.render("按空格键或R键重新开始", True, self.WHITE)
        continue_rect = continue_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 130))
        self.screen.blit(continue_text, continue_rect)
    
    def restart_game(self):
        """重新开始游戏"""
        # 重新创建迷宫和吃豆人
        self.maze = Maze(self.SCREEN_WIDTH, self.MAZE_HEIGHT * self.GRID_SIZE, self.GRID_SIZE)
        self.pacman = Pacman(1, 1, self.GRID_SIZE)
        self.find_start_position()

        # 根据当前难度重新创建幽灵
        self.create_ghosts()

        # 重置游戏状态（使用当前难度的生命数）
        difficulty = self.difficulty_levels[self.current_difficulty]
        self.game_state = "PLAYING"
        self.score = 0
        self.lives = difficulty["lives"]
        self.level = 1
        self.game_time = 0
        self.game_over = False
        self.game_won = False

    def reset_positions(self):
        """重置所有角色位置（死亡后复活用）"""
        self.find_start_position()
        # 重置幽灵位置
        ghost_positions = [(19, 9), (20, 9), (19, 10), (20, 10)]
        for i, ghost in enumerate(self.ghosts):
            if i < len(ghost_positions):
                x, y = ghost_positions[i]
                ghost.reset_position(x, y)

    def return_to_home(self):
        """返回首页"""
        # 保存当前游戏进度（如果需要的话）
        if self.game_state == "PLAYING":
            self.save_game_progress()

        # 返回开始界面
        self.game_state = "START"
    
    def run(self):
        """运行游戏主循环"""
        print("=" * 50)
        print("🎮 吃豆子游戏启动！")
        print("=" * 50)
        print("🎯 游戏目标：吃掉所有豆子获胜")
        print("🕹️  控制说明：")
        print("   - 方向键或WASD：移动吃豆人")
        print("   - 空格键：开始游戏/继续游戏")
        print("   - C键：继续保存的游戏")
        print("   - ESC键：暂停/继续游戏")
        print("   - S键：保存游戏进度")
        print("   - H键：返回首页")
        print("   - R键：重新开始游戏")
        print("🏆 计分规则：")
        print("   - 小豆子：10分")
        print("   - 大豆子：50分 + 40分奖励")
        print("   - 关卡完成：1000分 × 关卡数")
        print("=" * 50)

        while self.running:
            # 处理事件
            self.handle_events()

            # 更新游戏状态
            self.update()

            # 绘制画面
            self.draw()

            # 控制帧率
            self.clock.tick(self.FPS)

        # 保存最高分
        self.save_high_score()

        # 退出游戏
        print("🎮 感谢游玩！")
        print(f"🏆 最终分数：{self.score}")
        print(f"🥇 最高纪录：{self.high_score}")
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = PacmanGame()
    game.run()
