import pygame
import sys
from pacman import Pacman
from maze import Maze
from ghost import Ghost

class PacmanGame:
    def __init__(self):
        # 初始化pygame
        pygame.init()
        
        # 游戏设置
        self.GRID_SIZE = 20
        self.MAZE_WIDTH = 40
        self.MAZE_HEIGHT = 34
        self.SCREEN_WIDTH = self.MAZE_WIDTH * self.GRID_SIZE
        self.SCREEN_HEIGHT = self.MAZE_HEIGHT * self.GRID_SIZE + 100  # 额外空间显示分数
        
        # 创建屏幕
        self.screen = pygame.display.set_mode((self.SCREEN_WIDTH, self.SCREEN_HEIGHT))
        pygame.display.set_caption("吃豆子游戏 - Pacman")
        
        # 游戏时钟
        self.clock = pygame.time.Clock()
        self.FPS = 60
        
        # 创建游戏对象
        self.maze = Maze(self.SCREEN_WIDTH, self.MAZE_HEIGHT * self.GRID_SIZE, self.GRID_SIZE)
        self.pacman = Pacman(1, 1, self.GRID_SIZE)  # 在迷宫中找一个合适的起始位置

        # 创建幽灵
        self.ghosts = []
        ghost_colors = [(255, 0, 0), (255, 192, 203), (0, 255, 255), (255, 165, 0)]  # 红、粉、青、橙
        self.create_ghosts(ghost_colors)

        # 游戏状态
        self.score = 0
        self.lives = 3  # 生命数
        self.game_over = False
        self.game_won = False
        self.running = True
        
        # 字体
        self.font = pygame.font.Font(None, 36)
        self.big_font = pygame.font.Font(None, 72)
        
        # 颜色
        self.BLACK = (0, 0, 0)
        self.WHITE = (255, 255, 255)
        self.YELLOW = (255, 255, 0)
        self.RED = (255, 0, 0)
        self.GREEN = (0, 255, 0)
        
        # 找到一个合适的起始位置
        self.find_start_position()

    def create_ghosts(self, colors):
        """创建幽灵"""
        # 在迷宫中找到合适的幽灵起始位置
        ghost_positions = [(19, 9), (20, 9), (19, 10), (20, 10)]  # 中央区域

        for i, color in enumerate(colors):
            if i < len(ghost_positions):
                x, y = ghost_positions[i]
                # 确保位置不是墙壁
                if not self.maze.is_wall(x, y):
                    ghost = Ghost(x, y, self.GRID_SIZE, color)
                    self.ghosts.append(ghost)

    def find_start_position(self):
        """找到一个合适的起始位置（非墙壁位置）"""
        for y in range(len(self.maze.maze_layout)):
            for x in range(len(self.maze.maze_layout[0])):
                if not self.maze.is_wall(x, y):
                    self.pacman.grid_x = x
                    self.pacman.grid_y = y
                    self.pacman.pixel_x = x * self.GRID_SIZE + self.GRID_SIZE // 2
                    self.pacman.pixel_y = y * self.GRID_SIZE + self.GRID_SIZE // 2
                    return
    
    def handle_events(self):
        """处理游戏事件"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.running = False
                elif event.key == pygame.K_r and (self.game_over or self.game_won):
                    self.restart_game()
                elif not self.game_over and not self.game_won:
                    # 控制吃豆人移动
                    if event.key == pygame.K_RIGHT or event.key == pygame.K_d:
                        self.pacman.set_direction(0)  # 右
                    elif event.key == pygame.K_DOWN or event.key == pygame.K_s:
                        self.pacman.set_direction(1)  # 下
                    elif event.key == pygame.K_LEFT or event.key == pygame.K_a:
                        self.pacman.set_direction(2)  # 左
                    elif event.key == pygame.K_UP or event.key == pygame.K_w:
                        self.pacman.set_direction(3)  # 上
    
    def update(self):
        """更新游戏状态"""
        if not self.game_over and not self.game_won:
            # 更新吃豆人
            self.pacman.update(self.maze)
            
            # 检查是否吃到豆子
            points = self.pacman.eat_dot(self.maze)
            if points > 0:
                self.score += points
            
            # 检查是否获胜（所有豆子都被吃完）
            if self.maze.count_dots() == 0:
                self.game_won = True
    
    def draw(self):
        """绘制游戏画面"""
        # 清空屏幕
        self.screen.fill(self.BLACK)
        
        # 绘制迷宫
        self.maze.draw(self.screen)
        
        # 绘制吃豆人
        if not self.game_over:
            self.pacman.draw(self.screen)
        
        # 绘制分数
        score_text = self.font.render(f"分数: {self.score}", True, self.YELLOW)
        self.screen.blit(score_text, (10, self.MAZE_HEIGHT * self.GRID_SIZE + 10))
        
        # 绘制剩余豆子数量
        dots_left = self.maze.count_dots()
        dots_text = self.font.render(f"剩余豆子: {dots_left}", True, self.WHITE)
        self.screen.blit(dots_text, (10, self.MAZE_HEIGHT * self.GRID_SIZE + 50))
        
        # 绘制控制说明
        if not self.game_over and not self.game_won:
            control_text = self.font.render("使用方向键或WASD移动", True, self.WHITE)
            self.screen.blit(control_text, (self.SCREEN_WIDTH - 300, self.MAZE_HEIGHT * self.GRID_SIZE + 10))
        
        # 绘制游戏结束或获胜信息
        if self.game_won:
            win_text = self.big_font.render("恭喜获胜！", True, self.GREEN)
            text_rect = win_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2))
            self.screen.blit(win_text, text_rect)
            
            restart_text = self.font.render("按R键重新开始", True, self.WHITE)
            restart_rect = restart_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 50))
            self.screen.blit(restart_text, restart_rect)
        
        elif self.game_over:
            game_over_text = self.big_font.render("游戏结束", True, self.RED)
            text_rect = game_over_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2))
            self.screen.blit(game_over_text, text_rect)
            
            restart_text = self.font.render("按R键重新开始", True, self.WHITE)
            restart_rect = restart_text.get_rect(center=(self.SCREEN_WIDTH // 2, self.SCREEN_HEIGHT // 2 + 50))
            self.screen.blit(restart_text, restart_rect)
        
        # 更新显示
        pygame.display.flip()
    
    def restart_game(self):
        """重新开始游戏"""
        # 重新创建迷宫和吃豆人
        self.maze = Maze(self.SCREEN_WIDTH, self.MAZE_HEIGHT * self.GRID_SIZE, self.GRID_SIZE)
        self.pacman = Pacman(1, 1, self.GRID_SIZE)
        self.find_start_position()
        
        # 重置游戏状态
        self.score = 0
        self.game_over = False
        self.game_won = False
    
    def run(self):
        """运行游戏主循环"""
        print("吃豆子游戏启动！")
        print("控制说明：")
        print("- 使用方向键或WASD移动吃豆人")
        print("- 吃掉所有豆子获胜")
        print("- 按ESC退出游戏")
        print("- 游戏结束后按R重新开始")
        
        while self.running:
            # 处理事件
            self.handle_events()
            
            # 更新游戏状态
            self.update()
            
            # 绘制画面
            self.draw()
            
            # 控制帧率
            self.clock.tick(self.FPS)
        
        # 退出游戏
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = PacmanGame()
    game.run()
