#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试新的游戏界面布局
验证信息栏是否正确显示在顶部
"""

import pygame
import sys
from game import PacmanGame

def test_layout():
    """测试界面布局"""
    print("🧪 测试新的界面布局...")
    
    # 初始化pygame
    pygame.init()
    
    try:
        # 创建游戏实例
        game = PacmanGame()
        
        # 检查屏幕尺寸设置
        expected_height = game.MAZE_HEIGHT * game.GRID_SIZE + game.INFO_BAR_HEIGHT
        assert game.SCREEN_HEIGHT == expected_height, f"屏幕高度应为{expected_height}，实际为{game.SCREEN_HEIGHT}"
        
        # 检查信息栏高度
        assert hasattr(game, 'INFO_BAR_HEIGHT'), "应该有INFO_BAR_HEIGHT属性"
        assert game.INFO_BAR_HEIGHT == 80, f"信息栏高度应为80，实际为{game.INFO_BAR_HEIGHT}"
        
        print("✅ 屏幕尺寸设置正确")
        
        # 测试绘制方法是否支持偏移
        # 创建一个小的测试屏幕
        test_screen = pygame.Surface((800, 600))
        
        # 测试迷宫绘制
        game.maze.draw(test_screen, 80)  # 应该支持偏移参数
        print("✅ 迷宫绘制支持偏移")
        
        # 测试吃豆人绘制
        game.pacman.draw(test_screen, 80)  # 应该支持偏移参数
        print("✅ 吃豆人绘制支持偏移")
        
        # 测试幽灵绘制
        for ghost in game.ghosts:
            ghost.draw(test_screen, 80)  # 应该支持偏移参数
        print("✅ 幽灵绘制支持偏移")
        
        # 测试游戏信息绘制
        game.draw_game_info()
        print("✅ 游戏信息栏绘制正常")
        
        print("🎉 界面布局测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        pygame.quit()

def create_layout_demo():
    """创建布局演示图"""
    print("\n📐 新界面布局说明：")
    print("=" * 60)
    print("┌" + "─" * 58 + "┐")
    print("│" + " " * 20 + "顶部信息栏" + " " * 20 + "│")
    print("│ 分数: 1250  最高分: 2000  生命: 3  关卡: 1    │")
    print("│ 剩余豆子: 45  时间: 120s  控制提示          │")
    print("├" + "─" * 58 + "┤")
    print("│" + " " * 58 + "│")
    print("│" + " " * 20 + "游戏区域" + " " * 22 + "│")
    print("│" + " " * 15 + "（迷宫、吃豆人、幽灵）" + " " * 15 + "│")
    print("│" + " " * 58 + "│")
    print("│" + " " * 58 + "│")
    print("│" + " " * 58 + "│")
    print("│" + " " * 58 + "│")
    print("│" + " " * 58 + "│")
    print("│" + " " * 58 + "│")
    print("│" + " " * 58 + "│")
    print("│" + " " * 58 + "│")
    print("└" + "─" * 58 + "┘")
    print("=" * 60)
    
    print("\n🎨 布局特点：")
    print("✅ 信息栏固定在顶部，背景为深蓝色")
    print("✅ 分数、生命等重要信息一目了然")
    print("✅ 控制提示直接显示在界面上")
    print("✅ 游戏区域更加集中，视觉效果更好")
    print("✅ 所有游戏元素向下偏移80像素")

def show_comparison():
    """显示布局对比"""
    print("\n📊 布局改进对比：")
    print("=" * 60)
    print("🔴 旧布局：")
    print("   - 信息显示在底部")
    print("   - 分散用户注意力")
    print("   - 需要上下移动视线")
    print("   - 界面不够紧凑")
    print()
    print("🟢 新布局：")
    print("   - 信息显示在顶部")
    print("   - 信息集中，易于查看")
    print("   - 视线集中在上方区域")
    print("   - 界面更加专业美观")
    print("=" * 60)

def interactive_test_guide():
    """交互测试指南"""
    print("\n🎮 交互测试指南：")
    print("=" * 60)
    print("1. 启动游戏：python game.py")
    print("2. 观察开始界面（应该正常显示）")
    print("3. 按空格键开始游戏")
    print("4. 检查顶部信息栏：")
    print("   - 第一行：分数、最高分、生命、关卡")
    print("   - 第二行：剩余豆子、时间、控制提示")
    print("   - 背景：深蓝色，底部有白色分割线")
    print("5. 移动吃豆人，观察分数实时更新")
    print("6. 测试各种功能按键")
    print("7. 确认游戏区域正确显示在信息栏下方")
    print("=" * 60)

if __name__ == "__main__":
    print("🎨 游戏界面布局测试")
    print("=" * 60)
    
    # 运行自动化测试
    success = test_layout()
    
    # 显示布局说明
    create_layout_demo()
    show_comparison()
    interactive_test_guide()
    
    if success:
        print("\n🎉 布局测试通过！新的顶部信息栏已成功实现。")
        print("现在可以启动游戏查看新的界面布局效果。")
    else:
        print("\n❌ 布局测试失败，请检查代码。")
    
    print("\n🚀 启动游戏体验新布局：python game.py")
