# 🎮 吃豆子游戏 (Pacman Game)

一个用Python和Pygame开发的经典吃豆子游戏，包含完整的游戏功能和用户界面。

## 🎯 游戏特性

### 核心功能
- ✅ **开始界面** - 显示游戏标题、最高分和控制说明
- ✅ **游戏界面** - 完整的迷宫、吃豆人和幽灵
- ✅ **暂停功能** - 按ESC键暂停/继续游戏
- ✅ **结束界面** - 显示最终分数和重新开始选项
- ✅ **获胜界面** - 关卡完成时的庆祝界面

### 记分系统
- 🔸 **小豆子**: 10分
- 🔸 **大豆子**: 50分 + 40分奖励 = 90分
- 🔸 **关卡奖励**: 1000分 × 关卡数
- 🔸 **最高分记录**: 自动保存到文件
- 🔸 **新纪录提示**: 创造新纪录时显示特殊提示

### 游戏机制
- 🎮 **生命系统**: 3条生命，被幽灵抓到减少1条
- 👻 **智能幽灵**: 30%概率追踪玩家，70%随机移动
- 🏃 **平滑移动**: 基于像素的平滑移动系统
- 🎨 **动画效果**: 吃豆人嘴巴开合动画

## 🕹️ 游戏控制

| 按键 | 功能 |
|------|------|
| **方向键** 或 **WASD** | 移动吃豆人 |
| **空格键** | 开始游戏/继续游戏 |
| **ESC键** | 暂停/继续游戏 |
| **R键** | 重新开始游戏 |

## 🚀 运行游戏

### 环境要求
- Python 3.6+
- Pygame 2.0+

### 安装依赖
```bash
pip install pygame
```

### 中文字体支持
如果游戏中的中文无法正常显示，请安装中文字体：

**Linux系统：**
```bash
# 运行字体安装脚本
./install_fonts.sh

# 或手动安装
sudo apt-get install fonts-noto-cjk fonts-wqy-zenhei  # Ubuntu/Debian
sudo yum install google-noto-cjk-fonts wqy-zenhei-fonts  # CentOS/RHEL
sudo pacman -S noto-fonts-cjk wqy-zenhei  # Arch Linux
```

**Windows系统：**
- 通常已内置中文字体，确保安装了中文语言包

**macOS系统：**
- 通常已内置中文字体，如有问题可安装思源黑体

### 字体测试
运行字体测试程序检查中文显示：
```bash
python test_font.py
```

### 启动游戏
```bash
python game.py
```

## 📁 文件结构

```
pacman-game/
├── game.py          # 主游戏文件
├── pacman.py        # 吃豆人类
├── maze.py          # 迷宫类
├── ghost.py         # 幽灵类
├── high_score.txt   # 最高分记录文件（自动生成）
└── README.md        # 说明文档
```

## 🎨 游戏界面

### 开始界面
- 游戏标题和副标题
- 当前最高分显示
- 详细的控制说明
- 计分规则说明

### 游戏界面
- 经典的迷宫布局
- 黄色的吃豆人角色
- 四个不同颜色的幽灵（红、粉、青、橙）
- 实时游戏信息显示：
  - 当前分数
  - 最高分
  - 剩余生命数
  - 当前关卡
  - 剩余豆子数量
  - 游戏时间

### 暂停界面
- 半透明覆盖层
- 暂停提示文字
- 继续游戏说明

### 结束界面
- 游戏结束提示
- 最终分数显示
- 新纪录提示（如果适用）
- 重新开始选项

### 获胜界面
- 获胜庆祝文字
- 关卡完成提示
- 分数和奖励详情
- 新纪录提示（如果适用）

## 🏆 计分详情

### 基础分数
- 每个小豆子：10分
- 每个大豆子：50分基础 + 40分奖励 = 90分

### 奖励分数
- 关卡完成奖励：1000分 × 关卡数
- 例如：完成第1关获得1000分奖励

### 最高分系统
- 自动保存最高分到 `high_score.txt` 文件
- 创造新纪录时显示特殊提示
- 开始界面显示历史最高分

## 🎮 游戏技巧

1. **观察幽灵行为**: 幽灵有30%概率追踪你，70%随机移动
2. **优先吃大豆子**: 大豆子提供更多分数
3. **合理利用暂停**: 在危险时刻使用ESC暂停思考策略
4. **记住迷宫布局**: 熟悉迷宫有助于躲避幽灵

## 🔧 技术特性

- **面向对象设计**: 清晰的类结构和职责分离
- **状态管理**: 完整的游戏状态机制
- **事件驱动**: 响应式的用户输入处理
- **数据持久化**: 最高分自动保存和加载
- **中文字体支持**: 自动检测和加载系统中文字体
- **跨平台兼容**: 支持Linux、Windows、macOS
- **可扩展性**: 易于添加新功能和关卡

## 🛠️ 故障排除

### 中文显示问题
**问题**: 游戏界面中文显示为方块或乱码
**解决方案**:
1. 运行字体测试: `python test_font.py`
2. 安装中文字体: `./install_fonts.sh` (Linux)
3. 检查控制台输出的字体加载信息

### 游戏启动问题
**问题**: 游戏无法启动或报错
**解决方案**:
1. 确保Python版本 >= 3.6
2. 安装pygame: `pip install pygame`
3. 检查文件权限和路径

### 性能问题
**问题**: 游戏运行卡顿
**解决方案**:
1. 关闭其他占用资源的程序
2. 降低游戏窗口大小
3. 检查系统硬件配置

### 控制响应问题
**问题**: 按键响应延迟或无响应
**解决方案**:
1. 确保游戏窗口处于焦点状态
2. 检查键盘连接
3. 尝试不同的控制键（方向键/WASD）

## 📝 更新日志

### v1.0.0
- ✅ 实现基础游戏功能
- ✅ 添加开始、暂停、结束界面
- ✅ 完整的记分系统
- ✅ 最高分记录功能
- ✅ 智能幽灵AI
- ✅ 生命系统
- ✅ 游戏状态管理

---

🎉 **享受游戏吧！** 如果你创造了新的高分记录，记得截图分享！
