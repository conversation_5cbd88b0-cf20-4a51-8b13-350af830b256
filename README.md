# 🎮 吃豆子游戏 (Pacman Game)

一个用Python和Pygame开发的经典吃豆子游戏，包含完整的游戏功能和用户界面。

## 🎯 游戏特性

### 核心功能
- ✅ **开始界面** - 显示游戏标题、最高分和控制说明
- ✅ **难度选择** - 四个难度级别，适合不同水平的玩家
- ✅ **游戏界面** - 完整的迷宫、吃豆人和幽灵，实时显示分数
- ✅ **暂停功能** - 按ESC键暂停/继续游戏
- ✅ **结束界面** - 显示最终分数和重新开始选项
- ✅ **获胜界面** - 关卡完成时的庆祝界面
- ✅ **返回首页** - 随时按H键返回开始界面
- ✅ **进度保存** - 保存和加载游戏进度功能

### 记分系统
- 🔸 **小豆子**: 10分
- 🔸 **大豆子**: 50分 + 40分奖励 = 90分
- 🔸 **关卡奖励**: 1000分 × 关卡数
- 🔸 **最高分记录**: 自动保存到文件
- 🔸 **新纪录提示**: 创造新纪录时显示特殊提示

### 游戏机制
- 🎮 **生命系统**: 3条生命，被幽灵抓到减少1条
- 👻 **智能幽灵**: 30%概率追踪玩家，70%随机移动
- 🏃 **平滑移动**: 基于像素的平滑移动系统
- 🎨 **动画效果**: 吃豆人嘴巴开合动画

## 🕹️ 游戏控制

| 按键 | 功能 | 使用场景 |
|------|------|----------|
| **方向键** 或 **WASD** | 移动吃豆人 | 游戏中 |
| **↑↓方向键** | 选择难度 | 开始界面 |
| **回车键** 或 **空格键** | 开始游戏/继续游戏 | 开始界面、暂停时 |
| **C键** | 继续保存的游戏 | 开始界面 |
| **ESC键** | 暂停/继续游戏 | 游戏中 |
| **S键** | 保存游戏进度 | 游戏中、暂停时 |
| **H键** | 返回首页 | 游戏中、暂停时 |
| **R键** | 重新开始游戏 | 任何时候 |

## 🚀 运行游戏

### 环境要求
- Python 3.6+
- Pygame 2.0+

### 安装依赖
```bash
pip install pygame
```

### 中文字体支持
如果游戏中的中文无法正常显示，请安装中文字体：

**Linux系统：**
```bash
# 运行字体安装脚本
./install_fonts.sh

# 或手动安装
sudo apt-get install fonts-noto-cjk fonts-wqy-zenhei  # Ubuntu/Debian
sudo yum install google-noto-cjk-fonts wqy-zenhei-fonts  # CentOS/RHEL
sudo pacman -S noto-fonts-cjk wqy-zenhei  # Arch Linux
```

**Windows系统：**
- 通常已内置中文字体，确保安装了中文语言包

**macOS系统：**
- 通常已内置中文字体，如有问题可安装思源黑体

### 字体测试
运行字体测试程序检查中文显示：
```bash
python test_font.py
```

### 启动游戏
```bash
python game.py
```

## 📁 文件结构

```
pacman-game/
├── game.py              # 主游戏文件
├── pacman.py            # 吃豆人类
├── maze.py              # 迷宫类
├── ghost.py             # 幽灵类
├── test_font.py         # 字体测试工具
├── test_new_features.py # 新功能测试脚本
├── start_game.sh        # 游戏启动脚本
├── install_fonts.sh     # 字体安装脚本
├── high_score.txt       # 最高分记录文件（自动生成）
├── game_progress.json   # 游戏进度保存文件（自动生成）
└── README.md            # 说明文档
```

## 🎨 游戏界面

### 开始界面
- 游戏标题和副标题
- 当前最高分显示
- 详细的控制说明
- 计分规则说明

### 游戏界面
- **顶部信息栏**: 深蓝色背景，显示所有重要信息
  - 第一行：当前分数、最高分、剩余生命数、当前关卡
  - 第二行：剩余豆子数量、游戏时间、控制提示
- **游戏区域**: 经典的迷宫布局
- **角色设计**: 黄色的吃豆人角色
- **敌人设计**: 四个不同颜色的幽灵（红、粉、青、橙）
- **界面优化**: 信息集中在顶部，游戏区域更加突出

### 暂停界面
- 半透明覆盖层
- 暂停提示文字
- 继续游戏说明

### 结束界面
- 游戏结束提示
- 最终分数显示
- 新纪录提示（如果适用）
- 重新开始选项

### 获胜界面
- 获胜庆祝文字
- 关卡完成提示
- 分数和奖励详情
- 新纪录提示（如果适用）

## 🆕 新增功能

### 💾 游戏进度保存
- **自动保存**: 返回首页时自动保存当前进度
- **手动保存**: 游戏中按S键随时保存
- **继续游戏**: 开始界面按C键继续之前的游戏
- **保存内容**: 分数、生命数、关卡、游戏时间、角色位置、迷宫状态

### 🏠 返回首页功能
- **随时返回**: 游戏中按H键返回开始界面
- **智能保存**: 返回首页时自动保存当前进度
- **状态保持**: 返回后可以继续之前的游戏

### 📊 动态分数显示
- **顶部信息栏**: 游戏界面顶部显示所有重要信息
- **实时更新**: 分数、生命数等信息实时更新
- **完整信息**: 分数、最高分、生命数、关卡、剩余豆子、游戏时间
- **控制提示**: 直接在界面上显示快捷键操作说明
- **美观设计**: 深蓝色背景，信息布局清晰

## 🏆 计分详情

### 基础分数
- 每个小豆子：10分
- 每个大豆子：50分基础 + 40分奖励 = 90分

### 奖励分数
- 关卡完成奖励：1000分 × 关卡数
- 例如：完成第1关获得1000分奖励

### 最高分系统
- 自动保存最高分到 `high_score.txt` 文件
- 创造新纪录时显示特殊提示
- 开始界面显示历史最高分

## 🎮 游戏技巧

1. **观察幽灵行为**: 幽灵有30%概率追踪你，70%随机移动
2. **优先吃大豆子**: 大豆子提供更多分数
3. **合理利用暂停**: 在危险时刻使用ESC暂停思考策略
4. **记住迷宫布局**: 熟悉迷宫有助于躲避幽灵

## 🔧 技术特性

- **面向对象设计**: 清晰的类结构和职责分离
- **状态管理**: 完整的游戏状态机制
- **事件驱动**: 响应式的用户输入处理
- **数据持久化**: 最高分自动保存和加载
- **中文字体支持**: 自动检测和加载系统中文字体
- **跨平台兼容**: 支持Linux、Windows、macOS
- **可扩展性**: 易于添加新功能和关卡

## 🛠️ 故障排除

### 中文显示问题
**问题**: 游戏界面中文显示为方块或乱码
**解决方案**:
1. 运行字体测试: `python test_font.py`
2. 安装中文字体: `./install_fonts.sh` (Linux)
3. 检查控制台输出的字体加载信息

### 游戏启动问题
**问题**: 游戏无法启动或报错
**解决方案**:
1. 确保Python版本 >= 3.6
2. 安装pygame: `pip install pygame`
3. 检查文件权限和路径

### 性能问题
**问题**: 游戏运行卡顿
**解决方案**:
1. 关闭其他占用资源的程序
2. 降低游戏窗口大小
3. 检查系统硬件配置

### 控制响应问题
**问题**: 按键响应延迟或无响应
**解决方案**:
1. 确保游戏窗口处于焦点状态
2. 检查键盘连接
3. 尝试不同的控制键（方向键/WASD）

## 📝 更新日志

### v1.0.0
- ✅ 实现基础游戏功能
- ✅ 添加开始、暂停、结束界面
- ✅ 完整的记分系统
- ✅ 最高分记录功能
- ✅ 智能幽灵AI
- ✅ 生命系统
- ✅ 游戏状态管理

---

🎉 **享受游戏吧！** 如果你创造了新的高分记录，记得截图分享！
