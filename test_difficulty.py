#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试游戏难度功能
验证不同难度级别的参数设置和游戏体验
"""

import pygame
import sys
from game import PacmanGame

def test_difficulty_system():
    """测试难度系统"""
    print("🧪 测试难度系统...")
    
    # 初始化pygame
    pygame.init()
    
    try:
        # 创建游戏实例
        game = PacmanGame()
        
        # 检查难度级别定义
        expected_difficulties = ["EASY", "NORMAL", "HARD", "EXPERT"]
        actual_difficulties = list(game.difficulty_levels.keys())
        
        assert len(actual_difficulties) == 4, f"应该有4个难度级别，实际有{len(actual_difficulties)}个"
        
        for difficulty in expected_difficulties:
            assert difficulty in actual_difficulties, f"缺少难度级别: {difficulty}"
        
        print("✅ 难度级别定义正确")
        
        # 测试每个难度的参数
        for difficulty_key, difficulty in game.difficulty_levels.items():
            required_params = ["name", "description", "ghost_speed", "ghost_count", "chase_probability", "lives", "score_multiplier"]
            
            for param in required_params:
                assert param in difficulty, f"难度{difficulty_key}缺少参数: {param}"
            
            # 检查参数范围
            assert 1 <= difficulty["ghost_speed"] <= 3, f"幽灵速度应在1-3之间: {difficulty['ghost_speed']}"
            assert 1 <= difficulty["ghost_count"] <= 6, f"幽灵数量应在1-6之间: {difficulty['ghost_count']}"
            assert 0.0 <= difficulty["chase_probability"] <= 1.0, f"追踪概率应在0-1之间: {difficulty['chase_probability']}"
            assert 1 <= difficulty["lives"] <= 5, f"生命数应在1-5之间: {difficulty['lives']}"
            assert 0.5 <= difficulty["score_multiplier"] <= 3.0, f"分数倍率应在0.5-3.0之间: {difficulty['score_multiplier']}"
        
        print("✅ 难度参数设置正确")
        
        # 测试难度选择
        assert game.current_difficulty == "NORMAL", f"默认难度应为NORMAL，实际为{game.current_difficulty}"
        assert game.selected_difficulty_index == 1, f"默认选择索引应为1，实际为{game.selected_difficulty_index}"
        
        # 测试难度切换
        game.selected_difficulty_index = 0
        game.update_current_difficulty()
        assert game.current_difficulty == "EASY", f"切换后难度应为EASY，实际为{game.current_difficulty}"
        
        game.selected_difficulty_index = 3
        game.update_current_difficulty()
        assert game.current_difficulty == "EXPERT", f"切换后难度应为EXPERT，实际为{game.current_difficulty}"
        
        print("✅ 难度选择功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        pygame.quit()

def test_ghost_creation():
    """测试幽灵创建"""
    print("🧪 测试幽灵创建...")
    
    pygame.init()
    
    try:
        game = PacmanGame()
        
        # 测试不同难度的幽灵创建
        for difficulty_key in game.difficulty_levels.keys():
            game.current_difficulty = difficulty_key
            game.create_ghosts()
            
            expected_count = game.difficulty_levels[difficulty_key]["ghost_count"]
            actual_count = len(game.ghosts)
            
            assert actual_count == expected_count, f"难度{difficulty_key}应创建{expected_count}个幽灵，实际创建{actual_count}个"
            
            # 检查幽灵属性
            for ghost in game.ghosts:
                expected_speed = game.difficulty_levels[difficulty_key]["ghost_speed"]
                expected_chase_prob = game.difficulty_levels[difficulty_key]["chase_probability"]
                
                assert ghost.speed == expected_speed, f"幽灵速度应为{expected_speed}，实际为{ghost.speed}"
                assert ghost.chase_probability == expected_chase_prob, f"追踪概率应为{expected_chase_prob}，实际为{ghost.chase_probability}"
        
        print("✅ 幽灵创建功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        pygame.quit()

def test_score_multiplier():
    """测试分数倍率"""
    print("🧪 测试分数倍率...")
    
    pygame.init()
    
    try:
        game = PacmanGame()
        
        # 测试不同难度的分数计算
        test_cases = [
            ("EASY", 10, 10),      # 简单难度，1.0倍率
            ("NORMAL", 10, 10),    # 普通难度，1.0倍率
            ("HARD", 10, 15),      # 困难难度，1.5倍率
            ("EXPERT", 10, 20)     # 专家难度，2.0倍率
        ]
        
        for difficulty_key, base_points, expected_points in test_cases:
            game.current_difficulty = difficulty_key
            difficulty = game.difficulty_levels[difficulty_key]
            
            calculated_points = int(base_points * difficulty["score_multiplier"])
            assert calculated_points == expected_points, f"难度{difficulty_key}的分数计算错误: 期望{expected_points}，实际{calculated_points}"
        
        print("✅ 分数倍率功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        pygame.quit()

def show_difficulty_comparison():
    """显示难度对比"""
    print("\n📊 难度级别对比：")
    print("=" * 80)
    print(f"{'难度':<8} {'幽灵数':<6} {'速度':<4} {'追踪率':<8} {'生命':<4} {'分数倍率':<8} {'描述'}")
    print("-" * 80)
    
    game = PacmanGame()
    for key, diff in game.difficulty_levels.items():
        print(f"{diff['name']:<8} {diff['ghost_count']:<6} {diff['ghost_speed']:<4} {diff['chase_probability']:<8.1f} {diff['lives']:<4} {diff['score_multiplier']:<8.1f} {diff['description']}")
    
    print("=" * 80)

def interactive_test_guide():
    """交互测试指南"""
    print("\n🎮 交互测试指南：")
    print("=" * 60)
    print("1. 启动游戏：python game.py")
    print("2. 在开始界面使用↑↓键选择难度")
    print("3. 观察难度描述和参数变化")
    print("4. 按回车或空格开始游戏")
    print("5. 观察游戏界面顶部的难度显示")
    print("6. 体验不同难度的游戏感受：")
    print("   - 简单：2个慢速幽灵，5条生命")
    print("   - 普通：4个标准幽灵，3条生命")
    print("   - 困难：4个快速幽灵，2条生命，1.5倍分数")
    print("   - 专家：6个快速智能幽灵，1条生命，2倍分数")
    print("7. 测试分数倍率效果")
    print("8. 测试获胜界面的难度显示")
    print("=" * 60)

def run_all_tests():
    """运行所有测试"""
    print("🎯 游戏难度功能测试")
    print("=" * 60)
    
    tests = [
        test_difficulty_system,
        test_ghost_creation,
        test_score_multiplier
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！难度功能正常工作。")
        show_difficulty_comparison()
        interactive_test_guide()
        return True
    else:
        print("❌ 部分测试失败，请检查代码。")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    
    if success:
        print("\n🚀 启动游戏体验难度功能：python game.py")
        sys.exit(0)
    else:
        sys.exit(1)
