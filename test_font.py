#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pygame
import sys

def get_chinese_font(size):
    """获取支持中文的字体"""
    # 常见的中文字体路径列表（按优先级排序）
    chinese_fonts = [
        # Linux 系统字体（优先使用系统中存在的字体）
        "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc",
        "/usr/share/fonts/opentype/noto/NotoSansCJK-Bold.ttc",
        "/usr/share/fonts/truetype/arphic/ukai.ttc",
        "/usr/share/fonts/truetype/arphic/uming.ttc",
        "/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc",
        "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
        "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
        # Windows 系统字体
        "C:/Windows/Fonts/simhei.ttf",
        "C:/Windows/Fonts/simsun.ttc",
        "C:/Windows/Fonts/msyh.ttc",
        # macOS 系统字体
        "/System/Library/Fonts/PingFang.ttc",
        "/System/Library/Fonts/Hiragino Sans GB.ttc",
    ]
    
    # 尝试加载中文字体
    for font_path in chinese_fonts:
        try:
            font = pygame.font.Font(font_path, size)
            print(f"✅ 成功加载字体: {font_path}")
            return font, font_path
        except Exception as e:
            print(f"❌ 无法加载字体: {font_path} - {e}")
            continue
    
    # 如果没有找到中文字体，尝试系统默认字体
    try:
        # 尝试获取系统字体
        font = pygame.font.SysFont("simhei,simsun,microsoftyahei,pingfang,notosanscjk,wqyzenhei,wqymicrohei", size)
        if font:
            print("✅ 使用系统字体")
            return font, "系统字体"
    except Exception as e:
        print(f"❌ 无法加载系统字体: {e}")
    
    # 最后回退到默认字体
    print("⚠️  警告: 未找到中文字体，使用默认字体（可能无法正确显示中文）")
    return pygame.font.Font(None, size), "默认字体"

def test_font():
    """测试字体显示"""
    pygame.init()
    
    # 创建窗口
    screen = pygame.display.set_mode((800, 600))
    pygame.display.set_caption("中文字体测试")
    
    # 获取字体
    font, font_name = get_chinese_font(36)
    big_font, big_font_name = get_chinese_font(72)
    
    print(f"使用的字体: {font_name}")
    print(f"使用的大字体: {big_font_name}")
    
    # 测试文本
    test_texts = [
        "吃豆子游戏",
        "游戏开始",
        "分数: 1000",
        "生命: 3",
        "恭喜获胜！",
        "游戏结束",
        "按空格键开始",
        "English Text Test"
    ]
    
    # 颜色
    WHITE = (255, 255, 255)
    BLACK = (0, 0, 0)
    YELLOW = (255, 255, 0)
    RED = (255, 0, 0)
    GREEN = (0, 255, 0)
    
    clock = pygame.time.Clock()
    running = True
    
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    running = False
        
        # 清空屏幕
        screen.fill(BLACK)
        
        # 绘制标题
        title = big_font.render("中文字体测试", True, YELLOW)
        title_rect = title.get_rect(center=(400, 50))
        screen.blit(title, title_rect)
        
        # 绘制字体信息
        font_info = font.render(f"当前字体: {font_name}", True, WHITE)
        screen.blit(font_info, (50, 100))
        
        # 绘制测试文本
        colors = [YELLOW, GREEN, WHITE, RED, GREEN, RED, WHITE, WHITE]
        for i, text in enumerate(test_texts):
            color = colors[i % len(colors)]
            rendered_text = font.render(text, True, color)
            screen.blit(rendered_text, (50, 150 + i * 40))
        
        # 绘制说明
        instruction = font.render("按ESC退出", True, WHITE)
        screen.blit(instruction, (50, 500))
        
        pygame.display.flip()
        clock.tick(60)
    
    pygame.quit()
    print("字体测试完成")

if __name__ == "__main__":
    print("=" * 50)
    print("🔤 中文字体测试程序")
    print("=" * 50)
    test_font()
