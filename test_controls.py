#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试游戏控制功能
验证方向键和其他控制键是否正常工作
"""

import pygame
import sys
from game import PacmanGame

def test_key_mapping():
    """测试按键映射"""
    print("🧪 测试按键映射...")
    
    # 初始化pygame
    pygame.init()
    
    try:
        game = PacmanGame()
        
        # 测试方向键常量是否正确
        direction_keys = {
            pygame.K_UP: "上",
            pygame.K_DOWN: "下", 
            pygame.K_LEFT: "左",
            pygame.K_RIGHT: "右",
            pygame.K_w: "W",
            pygame.K_s: "S",
            pygame.K_a: "A",
            pygame.K_d: "D"
        }
        
        for key, name in direction_keys.items():
            assert key is not None, f"{name}键常量不应为None"
        
        print("✅ 按键常量定义正确")
        
        # 测试吃豆人方向设置
        initial_direction = game.pacman.direction
        
        # 模拟设置不同方向
        game.pacman.set_direction(0)  # 右
        assert game.pacman.next_direction == 0, "设置右方向失败"
        
        game.pacman.set_direction(1)  # 下
        assert game.pacman.next_direction == 1, "设置下方向失败"
        
        game.pacman.set_direction(2)  # 左
        assert game.pacman.next_direction == 2, "设置左方向失败"
        
        game.pacman.set_direction(3)  # 上
        assert game.pacman.next_direction == 3, "设置上方向失败"
        
        print("✅ 吃豆人方向设置正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        pygame.quit()

def test_difficulty_selection():
    """测试难度选择控制"""
    print("🧪 测试难度选择控制...")
    
    pygame.init()
    
    try:
        game = PacmanGame()
        
        # 测试初始状态
        initial_index = game.selected_difficulty_index
        initial_difficulty = game.current_difficulty
        
        # 模拟向下选择
        game.selected_difficulty_index = (game.selected_difficulty_index + 1) % len(game.difficulty_levels)
        game.update_current_difficulty()
        
        assert game.selected_difficulty_index != initial_index, "难度索引应该改变"
        
        # 模拟向上选择
        game.selected_difficulty_index = (game.selected_difficulty_index - 1) % len(game.difficulty_levels)
        game.update_current_difficulty()
        
        assert game.selected_difficulty_index == initial_index, "应该回到初始索引"
        assert game.current_difficulty == initial_difficulty, "应该回到初始难度"
        
        print("✅ 难度选择控制正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        pygame.quit()

def show_control_guide():
    """显示控制指南"""
    print("\n🎮 游戏控制指南：")
    print("=" * 60)
    print("📍 开始界面控制：")
    print("   ↑ 键 - 选择上一个难度")
    print("   ↓ 键 - 选择下一个难度")
    print("   回车/空格 - 开始游戏")
    print("   C 键 - 继续保存的游戏")
    print()
    print("📍 游戏中控制：")
    print("   ↑ 键 / W 键 - 向上移动")
    print("   ↓ 键 / S 键 - 向下移动")
    print("   ← 键 / A 键 - 向左移动")
    print("   → 键 / D 键 - 向右移动")
    print("   ESC 键 - 暂停/继续")
    print("   H 键 - 返回首页")
    print("   R 键 - 重新开始")
    print("   S 键 - 保存游戏（暂停时）")
    print()
    print("📍 暂停界面控制：")
    print("   ESC/空格 - 继续游戏")
    print("   S 键 - 保存游戏")
    print("   H 键 - 返回首页")
    print("=" * 60)

def interactive_test_instructions():
    """交互测试说明"""
    print("\n🧪 交互测试说明：")
    print("=" * 60)
    print("1. 启动游戏：python game.py")
    print()
    print("2. 测试开始界面：")
    print("   - 使用↑↓键选择难度")
    print("   - 观察难度选择的高亮效果")
    print("   - 确认难度参数显示正确")
    print()
    print("3. 测试游戏控制：")
    print("   - 按回车或空格开始游戏")
    print("   - 使用方向键控制吃豆人移动")
    print("   - 测试WASD键是否也能控制移动")
    print("   - 确认吃豆人响应正确")
    print()
    print("4. 测试其他功能：")
    print("   - 按ESC暂停游戏")
    print("   - 在暂停时按S保存游戏")
    print("   - 按H返回首页")
    print("   - 在首页按C继续游戏")
    print()
    print("5. 验证问题修复：")
    print("   - 确认上下方向键在游戏中正常工作")
    print("   - 确认上下方向键在开始界面选择难度正常")
    print("   - 确认没有按键冲突")
    print("=" * 60)

def run_control_tests():
    """运行控制测试"""
    print("🎮 游戏控制功能测试")
    print("=" * 60)
    
    tests = [
        test_key_mapping,
        test_difficulty_selection
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有控制测试通过！")
        show_control_guide()
        interactive_test_instructions()
        return True
    else:
        print("❌ 部分测试失败，请检查代码。")
        return False

if __name__ == "__main__":
    success = run_control_tests()
    
    if success:
        print("\n🚀 启动游戏测试控制功能：python game.py")
        print("💡 特别注意测试上下方向键在不同界面的功能")
        sys.exit(0)
    else:
        sys.exit(1)
