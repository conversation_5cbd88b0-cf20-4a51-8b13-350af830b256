#!/bin/bash

# 吃豆子游戏 - 中文字体安装脚本
# 用于确保系统有可用的中文字体

echo "=================================================="
echo "🔤 吃豆子游戏 - 中文字体安装脚本"
echo "=================================================="

# 检测操作系统
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "🐧 检测到Linux系统"
    
    # 检查是否已有中文字体
    if fc-list :lang=zh-cn | grep -q "Noto\|WenQuanYi\|AR PL"; then
        echo "✅ 系统已安装中文字体"
        echo "可用的中文字体："
        fc-list :lang=zh-cn | head -5
    else
        echo "❌ 未检测到中文字体，开始安装..."
        
        # 检测包管理器并安装字体
        if command -v apt-get &> /dev/null; then
            echo "使用apt-get安装字体..."
            sudo apt-get update
            sudo apt-get install -y fonts-noto-cjk fonts-wqy-zenhei fonts-wqy-microhei
        elif command -v yum &> /dev/null; then
            echo "使用yum安装字体..."
            sudo yum install -y google-noto-cjk-fonts wqy-zenhei-fonts wqy-microhei-fonts
        elif command -v dnf &> /dev/null; then
            echo "使用dnf安装字体..."
            sudo dnf install -y google-noto-cjk-fonts wqy-zenhei-fonts wqy-microhei-fonts
        elif command -v pacman &> /dev/null; then
            echo "使用pacman安装字体..."
            sudo pacman -S --noconfirm noto-fonts-cjk wqy-zenhei wqy-microhei
        else
            echo "⚠️  未检测到支持的包管理器"
            echo "请手动安装以下字体包之一："
            echo "- fonts-noto-cjk"
            echo "- fonts-wqy-zenhei"
            echo "- fonts-wqy-microhei"
        fi
        
        # 更新字体缓存
        echo "更新字体缓存..."
        fc-cache -fv
        
        # 再次检查
        if fc-list :lang=zh-cn | grep -q "Noto\|WenQuanYi\|AR PL"; then
            echo "✅ 中文字体安装成功！"
        else
            echo "❌ 字体安装可能失败，请手动检查"
        fi
    fi
    
elif [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 检测到macOS系统"
    echo "macOS通常已内置中文字体，如果游戏中文显示异常，请："
    echo "1. 确保系统语言设置包含中文"
    echo "2. 安装额外的中文字体（如思源黑体）"
    
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
    echo "🪟 检测到Windows系统"
    echo "Windows通常已内置中文字体，如果游戏中文显示异常，请："
    echo "1. 确保系统已安装中文语言包"
    echo "2. 检查字体文件是否存在于C:/Windows/Fonts/"
    
else
    echo "❓ 未知操作系统: $OSTYPE"
    echo "请手动安装中文字体"
fi

echo ""
echo "=================================================="
echo "🎮 字体安装完成！现在可以运行游戏了："
echo "python game.py"
echo "=================================================="
